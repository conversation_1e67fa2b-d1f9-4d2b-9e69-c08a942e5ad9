import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import vue from "@vitejs/plugin-vue";
import ckeditor5 from "@ckeditor/vite-plugin-ckeditor5";
import path from "node:path";
import { visualizer } from "rollup-plugin-visualizer";
import { bundleStats } from "rollup-plugin-bundle-stats";

import Icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";
import Components from "unplugin-vue-components/vite";

import tailwind from "tailwindcss";
import autoprefixer from "autoprefixer";

import type { PluginOption } from "vite";

const laravelOptions = {
  input: ["resources/ts/app.ts", "resources/ts/app-auditor.ts"],
  refresh: true
};

// https://vitejs.dev/config/
export default defineConfig({
  base: "/",
  // base: "/metronic8/vue/demo8/",
  css: {
    postcss: {
      plugins: [tailwind(), require("postcss-nested"), autoprefixer()]
    }
  },
  plugins: [
    vue({
      template: {
        transformAssetUrls: {
          base: null,
          includeAbsolute: false
        }
      }
    }),
    Components({
      dirs: ["resources/ts/src/components"],
      resolvers: [
        IconsResolver({
          prefix: "icon"
        }),
        (componentName) => {
          if (componentName.startsWith("Ai"))
            return { name: componentName, from: "@bradoctech/ai-ui" };
        }
      ]
    }),
    Icons({ scale: 1 }),
    laravel(laravelOptions),
    ckeditor5({ theme: require.resolve("@ckeditor/ckeditor5-theme-lark") }),
    visualizer({
      filename: "public/build/stats/stats-treemap.html",
      gzipSize: true,
      brotliSize: true,
      template: "treemap" // sunburst, treemap, network
    }) as PluginOption,
    bundleStats({
      outDir: "stats",
      html: true,
      json: true,
      baseline: false,
      baselineFilepath: "../../../.stats/_first-stats/baseline.json"
    })
  ],
  server: {
    hmr: {
      host: "localhost"
    }
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "resources/ts/src"),
      "~": path.join(__dirname, "/node_modules"),
      "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
      "@/api": path.resolve(__dirname, "resources/ts/src/api"),
      "@/view": path.resolve(__dirname, "resources/ts/src/view"),
      "@/core": path.resolve(__dirname, "resources/ts/src/core"),
      "@/components": path.resolve(__dirname, "resources/ts/src/components"),
      "@/services": path.resolve(__dirname, "resources/ts/src/services"),
      "@/enums": path.resolve(__dirname, "resources/ts/src/enums")
    }
  },
  build: {
    outDir: "public/build",
    chunkSizeWarningLimit: 3000,
    assetsInlineLimit: 0,
    target: ["es2020", "edge88", "firefox78", "chrome88", "safari14"],
    rollupOptions: {
      output: {
        assetFileNames: "assets/[name].[hash][extname]",
        entryFileNames: "assets/[name].[hash].js",
        chunkFileNames: (chunkInfo) => {
          if (
            chunkInfo.name === "index" ||
            chunkInfo.name === "app" ||
            chunkInfo.name === "analise" ||
            chunkInfo.name === "Card" ||
            chunkInfo.name === "list" ||
            chunkInfo.name === "listWithFilter" ||
            chunkInfo.name === "MainLayout" ||
            chunkInfo.name === "managing_unit" ||
            chunkInfo.name === "remessa"
          ) {
            const entryModule =
              chunkInfo.moduleIds[chunkInfo.moduleIds.length - 1];

            const segments = path.dirname(entryModule).split("/");

            const firstSegment = segments[segments.indexOf("ts") + 1];
            const lastSegment = segments[segments.length - 1];
            const filename = chunkInfo.name;

            chunkInfo.name = `${firstSegment}-${lastSegment}-${filename}`;

            return `assets/c-${chunkInfo.name}.[hash].js`;
          }

          return "assets/[name].[hash].js";
        }
      }
    }
  }
});
