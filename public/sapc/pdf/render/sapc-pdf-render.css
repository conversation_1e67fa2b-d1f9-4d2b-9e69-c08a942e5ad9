@media print {
  /* Page setting */

  @page {
    size: A4;
    margin: 3cm 2cm 2cm 3cm;

    @top-center {
      content: element(runningHeader);
      width: 100%;
    }

    @bottom-left {
      content: element(runningFooterLeft);
    }

    @bottom-right {
      content: counter(page);
      font-size: 12pt;
      padding-top: 0.6cm;
      padding-bottom: 0.6cm;
      text-align: right;
      vertical-align: bottom;
      font-family: "Times New Roman", serif;
    }
  }

  @page cover {
    @bottom-right {
      content: none;
    }

    @top-center {
      content: none;
    }

    @bottom-center {
      content: none;
    }
  }

  @page cover:first {
    margin: 0;

    @bottom-right {
      content: none;
    }

    @top-center {
      content: none;
    }

    @bottom-center {
      content: none;
    }
  }

  @page frontmatter {
    @bottom-right {
      content: none;
    }

    @top-center {
      content: none;
    }

    @bottom-center {
      content: none;
    }
  }

  .ai-ck-page {
    overflow: hidden;
    break-before: page;
    font-family: "Times New Roman", serif;
    font-size: 12pt;
    line-height: 1.2;
  }

  .page-break {
    break-before: page;
  }

  /* Cover styles */

  .ai-ck-page__cover {
    page: cover;
    width: 21cm;
    height: 29.7cm;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .ai-ck-page__cover-wrapper {
    margin: 3cm 2cm 2cm 3cm;
  }

  .ai-ck-page__cover-text {
    margin: 0;
    font-size: 12pt;
    line-height: 1.1;
    text-indent: 0;
  }

  .ai-ck-page__cover-heading1 {
    margin: 0;
    font-size: 40pt;
    line-height: 1.1;
  }

  .ai-ck-page__cover-heading2 {
    margin: 0;
    font-size: 28pt;
    line-height: 1.1;
  }

  .ai-ck-page__cover-heading3 {
    margin: 0;
    font-size: 18pt;
    text-transform: none;
    line-height: 1.1;
  }

  .ai-ck-page__cover-heading4 {
    margin: 0;
    font-size: 16pt;
    text-transform: none;
    line-height: 1.1;
  }

  .ai-ck-page__cover-heading5 {
    margin: 0;
    font-size: 14pt;
    text-transform: none;
    line-height: 1.1;
  }

  .ai-ck-page__cover-heading6 {
    margin: 0;
    font-size: 12pt;
    text-transform: none;
    line-height: 1.1;
  }

  /* Content Styles */

  /* Content headers and footers */

  .ai-ck-page__content-header--center {
    position: running(runningHeader);
    padding-top: 0.75cm;
    padding-bottom: 0.75cm;
  }

  .ai-ck-page__content-footer--left {
    position: running(runningFooterLeft);
    padding-top: 0.6cm;
    padding-bottom: 0.6cm;
  }

  .pagedjs_page
    .pagedjs_margin-top-center
    > .pagedjs_margin-content
    > .ai-ck-page__content-header--center {
    display: flex;
    justify-content: end;
    gap: 0.25cm;
    align-items: end;
  }

  .pagedjs_page
    .pagedjs_margin-bottom-left
    > .pagedjs_margin-content
    > .ai-ck-page__content-footer--left {
    display: flex;
    justify-content: start;
    gap: 0.25cm;
    align-items: end;
  }

  .ai-ck-page__content-header-center-image {
    display: block;
    max-height: 1.5cm;
  }

  .ai-ck-page__content-footer-left-image {
    display: block;
    max-height: 0.8cm;
  }

  .ai-ck-page__content-header-center-text {
    font-family: "Times New Roman", serif;
    font-size: 10pt;
    text-align: right;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ai-ck-page__content-footer-left-text {
    font-family: "Times New Roman", serif;
    font-size: 10pt;
    text-align: left;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Content body */

  p {
    font-size: 12pt;
    margin-top: 6pt;
    margin-bottom: 6pt;
  }

  blockquote {
    margin-left: 4cm;
    margin-right: 0;
    padding: 0;
    border: 0;
    font-style: normal;
  }

  blockquote p {
    font-size: 11pt;
    line-height: 1;
  }

  blockquote p.ai-ck-page__abnt-text {
    font-size: 11pt;
    line-height: 1;
    margin-top: 6pt;
    margin-bottom: 6pt;
    text-indent: 0cm;
  }

  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.5;
    margin-top: 36pt;
    margin-bottom: 36pt;
    font-weight: 700;
  }

  h1 {
    font-size: 18pt;
    line-height: 1.5;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 36pt;
  }

  h2 {
    font-size: 16pt;
  }

  h3 {
    font-size: 14pt;
  }

  h4 {
    font-size: 12pt;
  }

  h5 {
    font-size: 11pt;
  }

  h6 {
    font-size: 10pt;
  }

  ol,
  ul {
    margin-top: 6pt;
    margin-bottom: 6pt;
    padding-left: 24pt;

    li {
      margin-top: 3pt;
      margin-bottom: 3pt;
    }
  }

  table {
    width: 100%;
    border: 0;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: fixed;

    * {
      margin: 0;
    }
  }

  td {
    padding: 4pt;
    line-height: 1.2;
    border: 0;
  }

  td,
  th {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
  }

  .text-tiny {
    font-size: 0.7em;
  }

  .text-small {
    font-size: 0.85em;
  }

  .text-big {
    font-size: 1.4em;
  }

  .text-huge {
    font-size: 1.8em;
  }

  .marker-pink {
    background-color: var(--ck-highlight-marker-pink, #fc7899);
  }

  .marker-yellow {
    background-color: var(--ck-highlight-marker-yellow, #fdfd77);
  }

  .marker-green {
    background-color: var(--ck-highlight-marker-green, #62f962);
  }

  .marker-blue {
    background-color: var(--ck-highlight-marker-blue, #72ccfd);
  }

  .pen-red {
    color: var(--ck-highlight-pen-red, #e71313);
    background-color: transparent;
  }

  .pen-green {
    color: var(--ck-highlight-pen-green, #128a00);
    background-color: transparent;
  }

  .ai-ck-page__abnt-text {
    font-size: 12pt;
    line-height: 1.5;
    margin-top: 6pt;
    margin-bottom: 6pt;
    text-indent: 1.25cm;
  }

  .table {
    margin-top: 6pt;
    margin-bottom: 6pt;
    margin-left: 0;
    margin-right: 0;
    width: 100%;
  }

  .table .ai-ck-page__abnt-text {
    font-size: 12pt;
    line-height: 1.2;
    margin: 0;
    text-indent: 0cm;
  }

  .image {
    margin: 0.9em auto;

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
    }
  }

  img.image_resized {
    margin: 0;

    width: 100%;
    max-width: 100%;
    height: auto;
  }

  .image-style-side {
    float: right;
    margin: 0.9em 0 0.9em 1.5em;
  }

  /* TOC Styles */

  #table-of-content {
    page: frontmatter;
    break-before: page;
  }

  .table-of-content__title {
    margin: 0;
    font-size: 32pt;
    font-weight: 700;
    line-height: 1.5;
  }

  #list-toc-generated {
    list-style: none;
    padding: 0;
    margin-top: 25px;
  }

  #list-toc-generated .toc-element-level-1 {
    font-weight: bold;
    margin-top: 12pt;
  }

  #list-toc-generated .toc-element-level-2,
  #list-toc-generated .toc-element-level-3,
  #list-toc-generated .toc-element-level-4,
  #list-toc-generated .toc-element-level-5,
  #list-toc-generated .toc-element-level-6 {
    margin-left: 16pt;
    margin-top: 6pt;
  }

  #list-toc-generated .toc-element a {
    text-decoration: none;
    color: #000;
    font-size: 12pt;
    line-height: 1;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 6pt;
    align-items: self-end;
  }

  #list-toc-generated .toc-element a::after {
    content: " " target-counter(attr(href), page);
    float: right;
  }

  #list-toc-generated .toc-element .leader {
    min-width: 20pt;
    position: absolute;
    bottom: 2px;
    width: 100%;
    border-bottom: 1px dashed #000;
    display: block;
  }

  #list-toc-generated .toc-element a .toc-element__link-text-wrapper {
    position: relative;
    overflow: hidden;
    padding-right: 20pt;
  }

  #list-toc-generated .toc-element a .toc-element__link-text {
    z-index: 1;
    position: relative;
    background: #fff;
    padding-right: 2pt;
  }
}
