<!DOCTYPE html>

<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <title>Formularios de Analise</title>

    <style type="text/css">
        @font-face {
            font-family: 'Times New Roman';
            src: url({{ storage_path('fonts/times-new-roman.ttf') }}) format('truetype');
            font-weight: 400;
            font-style: normal;
         }
        @font-face {
            font-family: 'Times New Roman';
            src: url({{ storage_path('fonts/times-new-roman-bold.ttf') }}) format('truetype');
            font-weight: 700;
            font-style: normal;
         }
        @font-face {
            font-family: 'Times New Roman';
            src: url({{ storage_path('fonts/times-new-roman-italic.ttf') }}) format('truetype');
            font-weight: 400;
            font-style: italic;
         }
        @font-face {
            font-family: 'Times New Roman';
            src: url({{ storage_path('fonts/times-new-roman-bold-italic.ttf') }}) format('truetype');
            font-weight: 700;
            font-style: italic;
         }
    </style>
</head>

<body>
    @php
        $formularioContracapa = $formularios->first(function ($formulario) {
            return $formulario->modeloFormulario->contracapa;
        });

        $formularioCapa = $formularios->first(function ($formulario) {
            return $formulario->modeloFormulario->capa;
        });
    @endphp

    @if($formularioCapa)
        <section
            class="ai-ck-page ai-ck-page__cover"
            @style( [ "background-image: url('" . public_path('storage/' . $formularioCapa->modeloFormulario->arquivo_capa) . "')" ] )
        >
            <section class="ai-ck-page__cover-wrapper toc-ignore">
                {!! $formularioCapa->texto !!}
            </section>
        </section>
    @endif

    @if($formularioContracapa)
        <section
            class="ai-ck-page ai-ck-page__cover"
            @style( [ "background-image: url('" . public_path('storage/' . $formularioContracapa->modeloFormulario->arquivo_contracapa) . "')" ] )
        >
            <section class="ai-ck-page__cover-wrapper toc-ignore">
                {!! $formularioContracapa->texto !!}
            </section>
        </section>
    @endif

    <section id="table-of-content">
        <header class="table-of-content__title">Sumário</header>
        <nav id="table-of-content__nav"></nav>
    </section>

    @foreach($formularios as $formulario)
        @if(!$formulario->modeloFormulario->capa && !$formulario->modeloFormulario->contracapa)
            <section class="ai-ck-page ai-ck-page__content">
                @if($analise->modeloAnalise->cabecalho || $analise->modeloAnalise->imagem_cabecalho || $cabecalhoPadrao)
                    <header class="ai-ck-page__content-header--center">
                        @if($analise->modeloAnalise->cabecalho)
                            <span class="ai-ck-page__content-header-center-text">{!! $analise->modeloAnalise->cabecalho !!}</span>
                        @endif

                        @if($analise->modeloAnalise->imagem_cabecalho)
                            <img class="ai-ck-page__content-header-center-image" src="{{ public_path('storage/uploads/' . $analise->modeloAnalise->imagem_cabecalho) }}" alt="Cabeçalho Imagem" />
                        @endif

                        @if($cabecalhoPadrao && !$analise->modeloAnalise->cabecalho && !$analise->modeloAnalise->imagem_cabecalho)
                            <span class="ai-ck-page__content-header-center-text">{!! $cabecalhoPadrao !!}</span>
                        @endif
                    </header>
                @endif

                @if($analise->modeloAnalise->rodape || $analise->modeloAnalise->imagem_rodape)
                    <footer class="ai-ck-page__content-footer--left">
                        @if($analise->modeloAnalise->imagem_rodape)
                            <img class="ai-ck-page__content-footer-left-image" src="{{ public_path('storage/uploads/' . $analise->modeloAnalise->imagem_rodape) }}" alt="Rodapé Imagem" />
                        @endif

                        @if($analise->modeloAnalise->rodape)
                            <span class="ai-ck-page__content-footer-left-text">{!! $analise->modeloAnalise->rodape !!}</span>
                        @endif
                    </footer>
                @endif

                {!! $formulario->texto !!}
            </section>
        @endif
    @endforeach
</body>
</html>
