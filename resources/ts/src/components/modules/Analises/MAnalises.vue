<template>
  <AiStackLayout>
    <AiContainer>
      <AiContainerHeader>
        <AiContainerTitle>Filtrar</AiContainerTitle>
      </AiContainerHeader>

      <AiContainerContent>
        <div
          class="ai-grid-rows[repeat(1,auto)] ai-grid ai-grid-cols-5 ai-items-start ai-gap-x-4 ai-gap-y-2"
        >
          <v-autocomplete
            v-model="filter.exercicio"
            :items="options.exercicios"
            label="Exercício"
            :disabled="!options.exercicios.length"
            placeholder="Selecione o exercício"
            small-chips
            @update:modelValue="fetchData()"
            clearable
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
          />
          <v-autocomplete
            v-model="filter.poder"
            :items="options.poderes"
            label="Poder"
            placeholder="Selecione o Poder"
            small-chips
            :disabled="!options.poderes.length"
            @update:modelValue="fetchData()"
            clearable
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
          />
          <v-autocomplete
            v-model="filter.esfera"
            :items="options.esferas"
            label="Esfera"
            placeholder="Selecione o Esfera"
            small-chips
            clearable
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
            :disabled="!options.esferas.length"
            @update:modelValue="fetchData()"
          />
          <v-autocomplete
            v-model="filter.tipoUG"
            :items="options.tiposUG"
            label="Tipo UG"
            placeholder="Selecione o Tipo UG"
            small-chips
            clearable
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
            :disabled="!options.tiposUG.length"
            @update:modelValue="fetchData()"
          />
          <v-autocomplete
            v-model="filter.municipio"
            :items="options.municipios"
            label="Municipio"
            placeholder="Selecione o Municipio"
            small-chips
            clearable
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
            :disabled="!options.municipios.length"
            @update:modelValue="fetchData()"
            item-title="text"
            item-value="value"
          />
        </div>
        <div
          class="ai-grid-rows[repeat(1,auto)] ai-grid ai-grid-cols-3 ai-items-start ai-gap-x-4 ai-gap-y-2"
        >
          <v-autocomplete
            v-model="filter.tipoAnalise"
            :items="getTiposAnalises"
            label="Tipo de Análise de Conta"
            placeholder="Selecione o tipo de Análise de Conta"
            small-chips
            clearable
            :disabled="!getTiposAnalises"
            @update:modelValue="fetchData()"
            item-title="title"
            item-value="value"
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
          />
          <v-autocomplete
            v-model="filter.unidadeGestora"
            :items="options.unidadesGestoras"
            label="Unidade Gestora"
            placeholder="Selecione a Unidade Gestora"
            small-chips
            clearable
            @update:modelValue="fetchData()"
            item-title="text"
            item-value="value"
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
          />
          <v-autocomplete
            v-model="filter.status"
            :items="options.status"
            label="Status"
            placeholder="Selecione um status"
            small-chips
            clearable
            @update:modelValue="fetchData()"
            item-title="text"
            item-value="value"
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
          >
          </v-autocomplete>
        </div>
      </AiContainerContent>
      <AiContainerFooter
        ><AiButton @click="clearFilters">
          <AiIcon><IconSolarEraserOutline /></AiIcon>
          Limpar
        </AiButton></AiContainerFooter
      >
    </AiContainer>

    <AiContainer>
      <AiContainerContent>
        <v-data-table-server
          :headers="headers"
          :items="analises.data"
          v-model:options="dataTable.pagination"
          :items-length="dataTable.totalItems"
          :items-per-page="dataTable.pagination.itemsPerPage"
          no-data-text="Não foram encontradas análises nesta consulta"
          class="elevation-1"
          @update:options="updateOptions"
          :loading="loading"
        >
          <template v-slot:headers>
            <tr>
              <th
                v-for="header in headers"
                :key="header.value"
                class="text-center"
              >
                {{ header.title }}
              </th>
            </tr>
          </template>

          <template v-slot:[`item.data_criacao`]="{ item }">
            {{ formatDate(item.data_criacao) }}
          </template>
          <template v-slot:[`item.modelo.tipo_analise`]="{ item }">
            {{ formatTipo(item.modelo.tipo_analise) }}
          </template>
          <template v-slot:[`item.status`]="{ item }">
            <span :class="formatStatusColor(item.status)">
              {{ formatStatus(item.status) }}
            </span>
          </template>

          <template v-slot:[`item.actions`]="{ item }">
            <AiButton
              v-if="canManagerAnalise"
              class="ai-mr-2"
              @click="
                $router.push({
                  name: 'analise',
                  params: {
                    analiseId: item.id
                  }
                })
              "
              size="sm"
            >
              <AiIcon><IconSolarEyeOutline /></AiIcon>
              Ver
            </AiButton>

            <AiButton
              @click="printAnalise(item.id)"
              :disabled="isDownloading"
              size="sm"
            >
              <AiIcon><IconSolarPrinterOutline /></AiIcon>
              Imprimir
            </AiButton>
          </template>
        </v-data-table-server>
      </AiContainerContent>
    </AiContainer>
  </AiStackLayout>
</template>

<script>
  import Toaster from "@/components/Toaster.vue";
  import { VDataTableServer } from "vuetify/components/VDataTable";
  import SAPCApi from "@/api/auditor/sapc.js";
  import { defineComponent } from "vue";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { useDownloadFile } from "@/composables/global/useDownloadFile.ts";
  import { useModalDownloadStore } from "@/stores/global/modalDownloadStore";

  export default defineComponent({
    components: {
      VDataTableServer
    },
    props: {
      filtros: {
        default: null
      },
      filtrosRemessas: {
        default: null
      }
    },
    data() {
      return {
        processing: false,
        filterRemessas: null,
        filter: {
          exercicio: null,
          poder: this.$route.params?.filtros?.poder || [],
          esfera: this.$route.params?.filtros?.esfera || [],
          tipoUG: this.$route.params?.filtros?.tipoUG || [],
          municipio: this.$route.params?.filtros?.municipio || [],
          tipoAnalise: this.$route.params?.filtros?.tipoAnalise || [],
          unidadeGestora: this.$route.params?.filtros?.unidadeGestora || [],
          status: this.$route.params?.filtros?.status || []
        },
        options: {
          exercicios: [],
          poderes: [],
          esferas: [],
          tiposUG: [],
          municipios: [],
          tiposAnalises: ["governo", "gestao"],
          unidadesGestoras: [],
          status: [
            "Aguardando início",
            "Iniciada",
            "Finalizada Tempestiva",
            "Finalizada Intempestiva",
            "Atrasada"
          ]
        },
        headers: [
          {
            title: "Número",
            value: "id",
            align: "center",
            sortable: false
          },
          {
            title: "Exercício",
            value: "exercicio",
            align: "center",
            sortable: true
          },
          {
            title: "Data Criação",
            value: "data_criacao",
            align: "center",
            sortable: true
          },
          {
            title: "UG",
            value: "unidade_gestora.nome_com_cidade",
            align: "start"
          },
          { title: "Responsável", value: "assinatura", align: "start" },
          {
            title: "Tipo Análise",
            value: "modelo.tipo_analise",
            align: "center"
          },
          {
            title: "Situação",
            value: "status",
            align: "center",
            sortable: true
          },
          {
            title: "Ações",
            value: "actions",
            align: "center",
            width: "250px",
            sortable: false
          }
        ],
        dataTable: {
          totalItems: 0,
          pagination: {
            page: 1,
            itemsPerPage: 25
          }
        },
        unidades_gestoras: {},
        analises: {},
        loading: true,
        isComponentMounted: false,
        canManagerAnalise: true
      };
    },
    created() {
      this.filterRemessas = this.$route.params?.filtrosRemessas;
      this.fetchFilters();
    },
    mounted() {
      this.isComponentMounted = true;
    },
    methods: {
      filterExercicios(exercicios) {
        return exercicios.map((item) => {
          return {
            title: item.exercicio,
            value: item.exercicio
          };
        });
      },
      async fetchFilters() {
        const me = this;
        const filters = {
          poderes: true,
          esferas: true,
          tiposUG: true,
          municipios: true,
          tipoAnalise: true,
          auditorUGs: true
        };

        try {
          const [result1, result2] = await Promise.all([
            SAPCApi.fetchFilters(filters),
            SAPCApi.fetchFiltersAuditor(this.filter)
          ]);

          const data1 = result1.data.data;
          const data2 = result2.data.data;

          Object.keys(data1).map(function (fieldOptions) {
            me.options[fieldOptions] = data1[fieldOptions];
          });

          this.options.exercicios = this.filterExercicios(data2.exercicios);
        } catch (error) {
          console.error("Erro ao buscar filtros:", error);
        }
      },
      fetchData() {
        useFetchStore().setFetchState("fetching");
        this.loading = true;

        SAPCApi.fetchAnalises(this.dataTable.pagination, {
          ...this.filter,
          status: this.filterStatus
        })
          .then((success) => {
            this.analises = success.data;
            const meta = success.data.meta;
            this.dataTable.pagination.page = meta.current_page;
            this.dataTable.pagination.itemsPerPage = parseInt(meta.per_page);
            this.dataTable.totalItems = meta.total;
            this.canManagerAnalise = success.data.can_manager_analise;

            useFetchStore().setFetchState("done");
            this.loading = false;
          })
          .catch((error) => {
            useFetchStore().setFetchState("error");
            this.handleError(error);
            console.error(error);
          });
      },
      updateOptions(pagination) {
        this.dataTable.pagination = pagination;
        this.fetchData();
      },
      includeCityName(unidadeGestora) {
        const ugCidade = unidadeGestora.cidade;
        const ugNome = unidadeGestora.nome;
        return ugCidade
          ? `${ugNome} DE ${ugCidade.toUpperCase()}`
          : `${ugNome}`;
      },
      handleError(err) {
        if (err.message && err.message !== "") {
          Toaster.toast({
            message: err.message,
            status: err?.status ?? "warn"
          });
        }
      },
      clearFilters() {
        this.filter = {
          exercicio: null,
          poder: [],
          esfera: [],
          tipoUG: [],
          municipio: [],
          tipoAnalise: [],
          unidadeGestora: [],
          status: []
        };

        this.fetchData();
      },
      formatTipo(value) {
        if (!value) return "-";
        return value === "governo" ? "Governo" : "Gestão";
      },
      formatStatus(value) {
        if (!value) return "-";
        return value === "aguardando_inicio"
          ? "Aguardando Início"
          : value === "iniciada"
            ? "Iniciada"
            : value === "finalizada_tempestiva"
              ? "Finalizada Tempestiva"
              : value === "atrasada"
                ? "Atrasada"
                : value === "finalizada_intempestiva"
                  ? "Finalizada Intempestiva"
                  : value === "reaberta"
                    ? "Reaberta"
                    : "--";
      },
      formatStatusColor(value) {
        if (!value) return "-";
        return "text-black";
      },
      formatDate(value) {
        const d = new Date(value);
        return d.toLocaleString("pt-BR").substr(0, 10);
      },
      printAnalise(analiseId) {
        if (this.isDownloading) return;

        const analise = this.analises.data.find(
          (item) => item.id === analiseId
        );

        const municipio =
          analise?.unidade_gestora?.cidade || "município desconhecido";

        useDownloadFile({
          url: `${location.origin}/api/sapc/analise/${analiseId}/download`,
          fileName: `Prestação de Contas de Governo – Prefeitura Municipal de ${municipio}.pdf`
        });
      }
    },
    computed: {
      isDownloading() {
        return useModalDownloadStore().isDownloading;
      },
      getTiposAnalises() {
        const tiposAnalises = this.options.tiposAnalises;
        const arrayTipos = [];

        tiposAnalises.map((value) => {
          const mapTipos = {
            title: value === "governo" ? "Governo" : "Gestão",
            value: value
          };
          arrayTipos.push(mapTipos);
        });
        return arrayTipos;
      },
      filterStatus() {
        const statuses = {
          "Aguardando início": "aguardando_inicio",
          Atrasada: "atrasada",
          "Finalizada Tempestiva": "finalizada_tempestiva",
          "Finalizada Intempestiva": "finalizada_intempestiva",
          Iniciada: "iniciada"
        };

        return statuses[this.filter.status];
      }
    }
  });
</script>
