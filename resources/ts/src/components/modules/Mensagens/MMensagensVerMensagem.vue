<template>
  <AiStackLayout>
    <div class="ai-flex ai-items-center ai-justify-end ai-gap-4">
      <AiButton
        variant="outlined"
        @click="handleBackButtonClick"
        :disabled="!canBack"
      >
        <AiIcon><IconSolarArrowLeftOutline /></AiIcon>
        Voltar
      </AiButton>
    </div>

    <AiContainer>
      <Suspense @resolve="handleCMessageDetailsViewOnlyResolve">
        <CMessageDetailsViewOnly />

        <template #fallback>
          <CMessageDetailsViewOnlySkeleton elevation="0" />
        </template>
      </Suspense>
      <Suspense>
        <CMessageAnswares ref="cMessageAnswaresRef" />
      </Suspense>
      <Suspense v-if="canSendAnsware">
        <MessageCKEditor
          :data="editorData"
          :is-saving="loading"
          :autosave-data="autosaveData"
          :autosave-url="autosaveUrl"
          @update:data="handleEditorUpdateData"
          @ready="handleEditorReady"
          ref="cRespostaCKEditor"
        />
      </Suspense>

      <div
        class="ai-flex ai-items-center ai-justify-end ai-gap-4"
        v-if="canSendAnsware"
      >
        <AiButton @click="sendAnsware" :disabled="!messageText || loading">
          <AiIcon>
            <IconSolarRestartOutline v-show="loading" class="ai-animate-spin" />
            <IconSolarPlain2Outline v-show="!loading" />
          </AiIcon>
          Enviar Resposta
        </AiButton>
      </div>
    </AiContainer>

    <AiContainer>
      <Suspense>
        <CMessageAttachments @update:attachments="handleUpdateAttachments" />
      </Suspense>
    </AiContainer>
  </AiStackLayout>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from "vue";
  import { useRoute, useRouter } from "vue-router";
  import { useSendAnsware } from "@/composables/mensagens/useSendAnsware";
  import { useBackButton } from "@/composables/global/useBackButton";
  import { useMessageStore } from "@/stores/message/messageStore";
  import { useAuthUser } from "@/composables/auth/useAuthUser";
  import { useSetMessageAsRead } from "@/composables/mensagens/useSetMessageAsRead";
  import Toaster from "@/components/Toaster.vue";

  import type { DefineComponent } from "vue";

  const { params } = useRoute();
  const messageId = Number(params.id);
  const router = useRouter();
  const messageStore = useMessageStore();
  const { user } = useAuthUser();

  const canBack = computed(() => {
    return router.getRoutes().length > 1;
  });

  const messageHeader = computed(() => {
    return messageStore.messageHeader;
  });

  const currentAnsware = computed(() => {
    return messageStore.answare;
  });

  const autosaveUrl = computed(() => {
    const messageId = currentAnsware.value?.mensagem_id;
    const answareId = currentAnsware.value?.id;

    return messageId && answareId
      ? `/comunicacao/mensagens/${messageId}/respostas/${answareId}/autosave`
      : "";
  });

  const canSendAnsware = computed(() => {
    return (
      messageHeader.value?.permite_respostas &&
      messageHeader.value?.status !== "fechado"
    );
  });

  const allIsReady = computed(() => {
    return messageStore.answare && editorIsReady.value;
  });

  const editorData = ref("");
  const autosaveData = ref("");
  const messageText = ref("");
  const editorIsReady = ref(false);
  const cMessageAnswaresRef = ref<DefineComponent | null>(null);
  const cRespostaCKEditor = ref<DefineComponent | null>(null);
  const loading = ref(false);
  const listAttachments = ref([]);

  watch(
    () => allIsReady.value,
    (value) => {
      if (value) {
        autosaveData.value = currentAnsware.value?.descricao;
        editorData.value = " ";
      }
    }
  );

  const handleEditorReady = () => {
    editorIsReady.value = true;
  };

  const handleCMessageDetailsViewOnlyResolve = () => {
    const userType = user?.value?.type;
    const allowAnsware = messageHeader.value?.permite_respostas;
    const status = messageHeader.value?.status;

    userType === "Jurisdicionado" &&
      !allowAnsware &&
      status !== "fechado" &&
      setMessageAsRead();
  };

  const handleEditorUpdateData = (data: string) => {
    messageText.value = data;
  };

  const setMessageAsRead = async () => {
    const { error } = await useSetMessageAsRead(messageId);

    error?.value && console.error(error.value);
    !error?.value && messageStore.fetchMessageHeader(messageId);
  };

  const sendAnsware = async () => {
    loading.value = true;
    const { error } = await useSendAnsware(messageId, messageText.value);
    loading.value = false;

    if (error?.value) {
      Toaster.toast({
        message: "Erro ao enviar resposta!",
        status: "error"
      });

      console.error(error.value);
      return;
    }

    Toaster.toast({
      message: "Resposta enviada com sucesso!",
      status: "success"
    });

    messageText.value = "";
    editorData.value = " ";
    cRespostaCKEditor.value?.resetText();

    cMessageAnswaresRef.value && cMessageAnswaresRef.value.fetchData();
  };

  const { handleBackButtonClick } = useBackButton();

  const handleUpdateAttachments = (data: any) => {
    listAttachments.value = data.value;

    // TODO: chamar o método de atualização das respostas
    cMessageAnswaresRef.value && cMessageAnswaresRef.value.fetchData();
  };
</script>
