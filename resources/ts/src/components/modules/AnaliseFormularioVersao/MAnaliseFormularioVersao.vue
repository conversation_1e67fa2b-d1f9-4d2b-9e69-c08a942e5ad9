<template>
  <AiStackLayout>
    <AiContainer v-if="loading">
      <SkeletonTable />
    </AiContainer>

    <AiContainer v-else>
      <AiContainerHeader>
        <div class="ai-flex ai-items-center ai-justify-between">
          <AiContainerTitle>
            {{ analiseFormularioVersao?.formulario?.nome }}
          </AiContainerTitle>
          <span class="ai-text-base ai-font-semibold">
            versão atual: #{{ analiseFormularioVersao?.formulario?.versao }}
          </span>
        </div>
      </AiContainerHeader>

      <AiContainerContent>
        <AiTable bordered>
          <AiTableBody>
            <AiTableRow>
              <AiTableCell>
                <AiTableCellItem>
                  <AiTableCellItemLeft>
                    <AiIcon>
                      <IconSolarDocumentTextLinear />
                    </AiIcon>
                  </AiTableCellItemLeft>
                  <AiTableCellItemContent>
                    <AiListItemOverline>
                      Visualizando versão
                    </AiListItemOverline>
                    <AiListItemText>
                      #{{ analiseFormularioVersao?.versao }}
                    </AiListItemText>
                  </AiTableCellItemContent>
                </AiTableCellItem>
              </AiTableCell>

              <AiTableCell>
                <AiTableCellItem>
                  <AiTableCellItemLeft>
                    <AiIcon>
                      <IconSolarUserLinear />
                    </AiIcon>
                  </AiTableCellItemLeft>
                  <AiTableCellItemContent>
                    <AiListItemOverline>Autor</AiListItemOverline>
                    <AiListItemText>
                      {{ analiseFormularioVersao?.usuario?.name }}
                    </AiListItemText>
                  </AiTableCellItemContent>
                </AiTableCellItem>
              </AiTableCell>
              <AiTableCell>
                <AiTableCellItem>
                  <AiTableCellItemLeft>
                    <AiIcon>
                      <IconSolarCalendarLinear />
                    </AiIcon>
                  </AiTableCellItemLeft>
                  <AiTableCellItemContent>
                    <AiListItemOverline>
                      Data de Preenchimento
                    </AiListItemOverline>
                    <AiListItemText>
                      {{
                        analiseFormularioVersao?.data_preenchimento &&
                        formatDateTime(
                          analiseFormularioVersao?.data_preenchimento
                        )
                      }}
                    </AiListItemText>
                  </AiTableCellItemContent>
                </AiTableCellItem>
              </AiTableCell>
            </AiTableRow>
          </AiTableBody>
        </AiTable>
      </AiContainerContent>

      <AiContainerFooter>
        <AiButton variant="outlined" @click="backToAnaliseFormulario">
          <AiIcon>
            <IconSolarArrowLeftOutline />
          </AiIcon>
          Voltar
        </AiButton>

        <AiButton @click="restoreVersao()" :disabled="isFinished || processing">
          <AiIcon>
            <AiSpinner v-if="processing" />
            <IconSolarRestartOutline v-else />
          </AiIcon>
          Restaurar versão #{{ analiseFormularioVersao?.versao }}
        </AiButton>
      </AiContainerFooter>
    </AiContainer>

    <AiContainer v-if="loading">
      <SkeletonTable />
    </AiContainer>

    <div v-else class="ai-rounded-sm ai-bg-surface-container-low">
      <div ref="diffHeaderWrapperRef" class="ai-relative">
        <div ref="diffHeaderRef" class="ai-flex ai-flex-row">
          <div
            class="ai-w-1/2 ai-rounded-l-sm ai-border-r ai-border-outline-variant ai-bg-surface-container ai-p-[0.3125rem] ai-pl-[0.625rem] ai-text-base ai-font-bold"
          >
            Versão {{ analiseFormularioVersao?.versao }}
          </div>
          <div
            class="ai-w-1/2 ai-rounded-r-sm ai-bg-surface-container ai-p-[0.3125rem] ai-pl-[0.625rem] ai-text-base ai-font-bold"
          >
            Versão
            {{ analiseFormularioVersao?.formulario?.versao }} (atual)
          </div>
        </div>
      </div>

      <div
        class="ai-float-left ai-w-1/2 ai-overflow-x-auto ai-break-words ai-border-r ai-border-outline-variant ai-p-5"
        v-html="analiseFormularioVersao?.diff_current"
      />
      <div
        class="ai-float-right ai-w-1/2 ai-overflow-x-auto ai-break-words ai-p-5"
        v-html="analiseFormularioVersao?.diff_old"
      />
    </div>
  </AiStackLayout>
</template>

<script setup lang="ts">
  import "vue-diff/dist/index.css";
  import { ref, onMounted, onBeforeUnmount, computed, nextTick } from "vue";
  import { useRoute, useRouter } from "vue-router";
  import SAPCApi from "@/api/auditor/sapc.js";
  import Toaster from "@/components/Toaster.vue";
  import { useFetchStore } from "@/stores/global/fetchStore";

  interface Formulario {
    id: string;
    nome: string;
    analiseId: string;
    texto: string;
    versao: number;
    status?: string;
  }

  interface Usuario {
    id: string;
    name: string;
  }

  interface AnaliseFormularioVersao {
    id: string;
    nome: string;
    texto: string;
    analiseFomularioId: string;
    formulario: Formulario;
    usuario: Usuario;
    versao: number;
    diff_current: string;
    diff_old: string;
    data_preenchimento: string;
  }

  const router = useRouter();
  const route = useRoute();

  const analiseId = computed(() => Number(route.params?.analiseId) || null);
  const formularioId = computed(
    () => Number(route.params?.formularioId) || null
  );
  const formularioVersaoId = computed(
    () => Number(route.params?.formularioVersaoId) || null
  );
  const isFinished = computed(
    () => analiseFormularioVersao.value?.formulario?.status === "finalizado"
  );

  const isLoadingPage = ref(true);
  const loading = ref(true);
  const processing = ref(false);
  const analiseFormularioVersao = ref<AnaliseFormularioVersao | null>(null);
  const diffHeaderWrapperRef = ref<HTMLElement | null>(null);
  const diffHeaderRef = ref<HTMLElement | null>(null);

  const formatDateTime = (value: string): string => {
    const d = new Date(value);
    return d.toLocaleString("pt-BR");
  };

  const showLoadingPageSkeleton = (): void => {
    isLoadingPage.value = true;
  };

  const hideLoadingPageSkeleton = (): void => {
    isLoadingPage.value = false;
  };

  const backToAnalises = (): void => {
    router.replace("/e-contas/analises");
  };

  const backToAnaliseFormulario = (): void => {
    if (analiseFormularioVersao.value && analiseId.value) {
      router.push(
        `/e-contas/analise/${analiseId.value}/formulario/${analiseFormularioVersao.value.formulario.id}`
      );
    }
  };

  const onResizeOrScroll = (): void => {
    if (!diffHeaderWrapperRef.value || !diffHeaderRef.value) return;

    const top =
      window.innerWidth >= 992
        ? window.scrollY +
          diffHeaderWrapperRef.value.getBoundingClientRect().top -
          74
        : window.scrollY +
          diffHeaderWrapperRef.value.getBoundingClientRect().top -
          55;

    diffHeaderRef.value.style.width = `${
      diffHeaderWrapperRef.value.getBoundingClientRect().width
    }px`;

    if (window.scrollY >= top) {
      diffHeaderRef.value.classList.add("ai-fixed", "ai-z-10");
      diffHeaderRef.value.style.top =
        window.innerWidth >= 992 ? "74px" : "55px";
    } else {
      diffHeaderRef.value.classList.remove("ai-fixed", "ai-z-10");
    }
  };

  const fetchData = async (): Promise<void> => {
    const fetchStore = useFetchStore();
    fetchStore.setFetchState("fetching");
    loading.value = true;
    processing.value = true;
    showLoadingPageSkeleton();

    try {
      const objFormularioVersao = await SAPCApi.fetchAnaliseFormularioVersao(
        analiseId.value,
        formularioId.value,
        formularioVersaoId.value
      );

      analiseFormularioVersao.value = objFormularioVersao.data;
      fetchStore.setFetchState("done");
      loading.value = false;
      processing.value = false;
      hideLoadingPageSkeleton();
    } catch (error) {
      fetchStore.setFetchState("error");
      backToAnalises();
      loading.value = false;
      processing.value = false;
      hideLoadingPageSkeleton();
      console.error(error);
    }
  };

  const restoreVersao = async (): Promise<void> => {
    if (!analiseFormularioVersao.value) return;

    const versao = analiseFormularioVersao.value;

    const answer = confirm(`Deseja restaurar a versão: #${versao.versao} ?`);

    if (!answer) return;

    processing.value = true;

    try {
      await SAPCApi.restoreFormularioVersao(
        analiseId.value,
        formularioId.value,
        formularioVersaoId.value
      );

      loading.value = false;
      processing.value = false;
      Toaster.toast({
        message: `Versão: #${versao.versao} restaurada com sucesso!`,
        status: "success"
      });
      backToAnaliseFormulario();
    } catch (error) {
      loading.value = false;
      processing.value = false;
      console.error(error);
    }
  };

  onMounted(() => {
    if (!analiseId.value || !formularioId.value || !formularioVersaoId.value) {
      backToAnalises();
      return;
    } else {
      fetchData();
    }

    window.addEventListener("resize", onResizeOrScroll);
    window.addEventListener("scroll", onResizeOrScroll);

    // Atualize o layout após o DOM ter sido renderizado
    nextTick(() => {
      onResizeOrScroll();
    });
  });

  onBeforeUnmount(() => {
    window.removeEventListener("resize", onResizeOrScroll);
    window.removeEventListener("scroll", onResizeOrScroll);
  });
</script>

<style lang="postcss">
  .vue-diff-theme-custom {
    code {
      @apply ai-text-on-surface ai-shadow-none;
    }
  }
  .vue-diff-viewer .vue-diff-row .vue-diff-cell-removed {
    @apply ai-bg-transparent;

    & span.modified {
      @apply ai-bg-red-200;
    }
  }
  .vue-diff-viewer .vue-diff-row .vue-diff-cell-added {
    @apply ai-bg-transparent;

    & span.modified {
      @apply ai-bg-green-100;
    }
  }

  .vue-diff-viewer .vue-diff-row .hljs {
    @apply ai-text-sm;
  }

  /* Difference Highlighting and Strike-through */
  ins {
    @apply ai-bg-green-100 ai-text-gray-800 ai-no-underline;
  }
  del {
    @apply ai-bg-red-100 ai-text-red-800 ai-line-through;
  }

  /* Image Diffing */
  del.diffimg.diffsrc {
    @apply ai-relative ai-inline-block;
  }
  del.diffimg.diffsrc:before {
    @apply ai-absolute ai-left-0 ai-top-0 ai-h-full ai-w-full ai-content-[""];
    background: repeating-linear-gradient(
        to left top,
        rgba(255, 0, 0, 0),
        rgba(255, 0, 0, 0) 49.5%,
        rgba(255, 0, 0, 1) 49.5%,
        rgba(255, 0, 0, 1) 50.5%
      ),
      repeating-linear-gradient(
        to left bottom,
        rgba(255, 0, 0, 0),
        rgba(255, 0, 0, 0) 49.5%,
        rgba(255, 0, 0, 1) 49.5%,
        rgba(255, 0, 0, 1) 50.5%
      );
  }

  /* List Diffing */
  /* List Styles */
  .diff {
    @apply ai-table ai-list-none;
    counter-reset: section;
  }
  .diff > li.normal,
  .diff > li.removed,
  .diff > li.replacement {
    @apply ai-table-row;
  }
  .diff > li > div {
    @apply ai-inline;
  }
  .diff > li.replacement:before,
  .diff > li.new:before {
    @apply ai-bg-green-100 ai-text-gray-800 ai-no-underline;
  }
  .diff > li.removed:before {
    @apply ai-bg-red-100 ai-text-red-800 ai-line-through;
    counter-increment: section;
  }

  /* List Counters / Numbering */
  .diff > li.normal:before,
  .diff > li.removed:before,
  .diff > li.replacement:before {
    @apply ai-table-cell ai-w-[15px] ai-overflow-hidden ai-pl-[1em] ai-indent-[-1em];
    content: counters(section, ".") ". ";
  }
  .diff > li.normal:before,
  li.replacement + li.replacement:before,
  .diff > li.replacement:first-child:before {
    counter-increment: section;
  }

  /* Exception Lists */
  ul.exception,
  ul.exception li:before {
    @apply ai-list-none;
    content: none;
  }
  .diff ul.exception ol {
    @apply ai-list-none;
    counter-reset: exception-section;
  }
  .diff ul.exception ol > li:before {
    counter-increment: exception-section;
    content: counters(exception-section, ".") ".";
  }

  ol.diff li.removed + li.replacement {
    counter-increment: none;
  }
  ol.diff li.removed + li.removed + li.replacement {
    counter-increment: section -1;
  }
  ol.diff li.removed + li.removed + li.removed + li.replacement {
    counter-increment: section -2;
  }
  ol.diff li.removed + li.removed + li.removed + li.removed + li.replacement {
    counter-increment: section -3;
  }
  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -4;
  }
  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -5;
  }
  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -6;
  }
  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -7;
  }
  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -8;
  }
  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -9;
  }
  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -10;
  }
  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -11;
  }
</style>
