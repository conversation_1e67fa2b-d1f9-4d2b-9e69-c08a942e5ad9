<template>
  <AiStackLayout>
    <AiContainer v-if="isRemessaInadimplenteAlertVisible">
      <AiContainerHeader>
        <div class="ai-flex ai-flex-row ai-gap-2">
          <AiIcon>
            <IconSolarDangerTriangleOutline />
          </AiIcon>
          <AiContainerTitle>Remessas Inadimplentes</AiContainerTitle>
        </div>
      </AiContainerHeader>

      <AiContainerContent>
        Análise não pode ser realizada, existe(m) Remessas Inadimplentes.
      </AiContainerContent>
    </AiContainer>

    <template v-else>
      <AiContainer>
        <AiContainerHeader>
          <div class="ai-flex ai-items-center ai-justify-between">
            <span class="ai-text-2xl ai-font-normal">
              {{ analiseFormulario.nome || "Formulário" }}
            </span>
            <span class="ai-text-base ai-font-semibold"
              >versão do documento: {{ analiseFormulario.versao }}</span
            >
          </div>
        </AiContainerHeader>

        <AiContainerContent>
          <Suspense>
            <AiCkeditor
              :data="analiseFormulario.texto"
              :disabled="isFinished"
              :autosave-url="autosaveUrl"
              :autosaveData="autosaveData"
              autosave-http-method="PUT"
              :is-saving="editor.isSaving"
              :fixed-toolbar-top-margin="116"
              @update:data="handleEditorUpdateData"
              @status-change="handleEditorStatusChange"
              @ready="handleEditorReady"
            />
          </Suspense>
        </AiContainerContent>

        <AiContainerFooter>
          <div class="ai-flex ai-w-full ai-items-center ai-justify-between">
            <span v-if="analiseFormulario.user_finalizado" class="w-full">
              {{ analiseFormulario.user_finalizado }}
            </span>

            <div
              class="ai-flex ai-w-full ai-items-center ai-justify-end ai-gap-4"
            >
              <AiButton
                variant="outlined"
                @click="save()"
                :disabled="
                  isFinished ||
                  processing ||
                  editor.status.isBusy ||
                  !editor.status.isDirty
                "
              >
                <AiIcon>
                  <AiSpinner v-if="processing" />
                  <IconSolarSdCardOutline v-else />
                </AiIcon>
                Salvar
              </AiButton>

              <AiButton
                v-if="isFinished"
                :disabled="processing || editor.status.isBusy"
                @click="reOpenForm()"
              >
                <AiIcon>
                  <AiSpinner v-if="processing" />
                  <IconSolarFolderOpenOutline v-else />
                </AiIcon>
                Reabrir Formulário
              </AiButton>

              <AiButton
                v-else
                :disabled="processing || editor.status.isBusy"
                @click="saveAndFinish()"
              >
                <AiIcon>
                  <AiSpinner v-if="processing" />
                  <IconSolarUnreadOutline v-else />
                </AiIcon>
                Concluir
              </AiButton>
            </div>
          </div>
        </AiContainerFooter>
      </AiContainer>

      <AiContainer>
        <AiContainerHeader>
          <div class="ai-flex ai-w-full ai-items-center ai-justify-between">
            <AiContainerTitle>Anexos</AiContainerTitle>

            <AiButton
              variant="outlined"
              :disabled="isFinished || processing"
              @click="modalAttachmentIsVisible = true"
            >
              <AiIcon>
                <AiSpinner v-if="processing" />
                <IconSolarPaperclipOutline v-else />
              </AiIcon>
              Anexar Documento
            </AiButton>
          </div>
        </AiContainerHeader>

        <AiContainerContent>
          <v-data-table-server
            v-show="!loadingAnexos"
            :headers="headersAnexo"
            :items="listaAnexos"
            v-model:options="dataTableAnexo.pagination"
            :items-length="dataTableAnexo.totalItems"
            :items-per-page="dataTableAnexo.pagination.itemsPerPage"
            @update:options="updateAnexos"
            no-data-text="Não existem anexos neste formulário"
            class="custom-table-managingUnit elevation-1"
          >
            <template v-slot:headers>
              <tr>
                <th
                  v-for="header in headersAnexo"
                  :key="header.value"
                  class="text-center"
                >
                  {{ header.title }}
                </th>
              </tr>
            </template>
            <template v-slot:loading>
              <div class="p-5">Carregando anexos ...</div>
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              {{ formatDate(item.created_at) }}
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <div class="ai-flex ai-items-center ai-gap-2">
                <AiButton
                  variant="outlined"
                  @click="downloadAnexo(item.analise_formulario_id, item.id)"
                >
                  <AiIcon>
                    <IconSolarEyeOutline />
                  </AiIcon>
                  Download
                </AiButton>

                <AiButton
                  variant="outlined"
                  @click="excluirAnexo(item)"
                  :disabled="isFinished"
                >
                  <AiIcon><IconSolarTrashBinMinimalisticOutline /></AiIcon>

                  <span class="ml-1">Excluir</span>
                </AiButton>
              </div>
            </template>
          </v-data-table-server>
        </AiContainerContent>
      </AiContainer>

      <AiContainer>
        <AiContainerHeader>
          <AiContainerTitle>Versões Anteriores</AiContainerTitle>
        </AiContainerHeader>

        <AiContainerContent>
          <v-data-table-server
            v-show="!loadingVersoes"
            :headers="headersVersao"
            :items="listaVersoes"
            v-model:options="dataTableVersao.pagination"
            :items-length="dataTableVersao.totalItems"
            :items-per-page="dataTableVersao.pagination.itemsPerPage"
            @update:options="updateVersoes"
            no-data-text="Não existem versões registradas neste formulário"
            class="custom-table-managingUnit elevation-1"
          >
            <template v-slot:headers>
              <tr>
                <th
                  v-for="header in headersVersao"
                  :key="header.value"
                  class="text-center"
                >
                  {{ header.title }}
                </th>
              </tr>
            </template>
            <template v-slot:loading>
              <div class="p-5">Carregando versões ...</div>
            </template>
            <template v-slot:[`item.data_preenchimento`]="{ item }">
              {{ formatDateTime(item.data_preenchimento) }}
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <AiButton
                variant="outlined"
                @click="verFormularioVersao(item.id)"
              >
                <AiIcon>
                  <IconSolarEyeOutline />
                </AiIcon>
                Ver
              </AiButton>
            </template>
          </v-data-table-server>
        </AiContainerContent>
      </AiContainer>

      <AiContainer>
        <AiContainerHeader>
          <AiContainerTitle>
            Histórico de Alterações da Análise
          </AiContainerTitle>
        </AiContainerHeader>

        <AiContainerContent>
          <v-data-table-server
            v-show="!loadingHistorico"
            :headers="headersHistorico"
            :items="historico"
            v-model:options="dataTableHistorico.pagination"
            :items-length="dataTableHistorico.totalItems"
            :items-per-page="dataTableHistorico.pagination.itemsPerPage"
            @update:options="updateHistorico"
            no-data-text="Sem histórico registrado neste formulário"
            class="custom-table-managingUnit elevation-1"
          >
            <template v-slot:headers>
              <tr>
                <th
                  v-for="header in headersHistorico"
                  :key="header.value"
                  class="text-center"
                >
                  {{ header.title }}
                </th>
              </tr>
            </template>
            <template v-slot:loading>
              <div class="p-5">
                Carregando histórico de alterações da análise ...
              </div>
            </template>
            <template v-slot:[`item.data_acao`]="{ item }">
              {{ formatDateTime(item.data_acao) }}
            </template>
          </v-data-table-server>
        </AiContainerContent>
      </AiContainer>
    </template>

    <CModalAttachment
      v-model="modalAttachmentIsVisible"
      title="Anexar Documentos"
      required-file-description
      @close="handleModalAttachmentClose"
    />
  </AiStackLayout>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, nextTick } from "vue";
  import { useRouter, useRoute } from "vue-router";
  import SAPCApi from "@/api/auditor/sapc.js";
  import { VDataTableServer } from "vuetify/components/VDataTable";
  import Toaster from "@/components/Toaster.vue";
  import { useAnaliseStore } from "@/stores/auditor/analiseStore";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { useMenuSapcStore } from "@/stores/auditor/menuSapcStore";
  import type {
    AnaliseFormulario,
    Anexo,
    FormularioVersao,
    HistoricoItem,
    DataTableState,
    EditorState,
    TableHeader,
    EditorStatus
  } from "./types";

  const route = useRoute();
  const router = useRouter();

  if (!route.params.analiseId || !route.params.formularioId) {
    router.replace({ name: "analises" });
  }

  // Stores
  const analiseStore = useAnaliseStore();
  const menuSapcStore = useMenuSapcStore();
  const fetchStore = useFetchStore();

  // State
  const isLoadingPage = ref(false);
  const isAnexosFetched = ref(false);
  const loading = ref(true);
  const loadingAnexos = ref(false);
  const loadingVersoes = ref(false);
  const loadingHistorico = ref(false);
  const isRemessaInadimplenteAlertVisible = ref(false);
  const processing = ref(false);
  const texto = ref("");
  const modalAttachmentIsVisible = ref(false);

  // Reactive objects
  const analiseFormulario = ref<AnaliseFormulario>({
    id: "",
    nome: "",
    analiseId: ""
  });

  const listaAnexos = ref<Anexo[]>([]);
  const dataTableAnexo = reactive<DataTableState>({
    totalItems: 0,
    pagination: {
      page: 1,
      itemsPerPage: 25
    }
  });

  const listaVersoes = ref<FormularioVersao[]>([]);
  const dataTableVersao = reactive<DataTableState>({
    totalItems: 0,
    pagination: {
      page: 1,
      itemsPerPage: 25
    }
  });

  const historico = ref<HistoricoItem[]>([]);
  const dataTableHistorico = reactive<DataTableState>({
    totalItems: 0,
    pagination: {
      page: 1,
      itemsPerPage: 25
    }
  });

  const editor = reactive<EditorState>({
    status: {
      isBusy: false,
      isDirty: false
    },
    isSaving: false
  });

  // Headers para as tabelas
  const headersAnexo = ref<TableHeader[]>([
    {
      title: "Número",
      value: "id",
      align: "center",
      sortable: false,
      width: "100px"
    },
    {
      title: "Arquivo",
      value: "arquivo",
      align: "center",
      sortable: true
    },
    {
      title: "Descrição",
      value: "descricao",
      align: "center",
      sortable: true
    },
    {
      title: "Usuário",
      value: "usuario.name",
      align: "center",
      sortable: true
    },
    {
      title: "Ações",
      value: "actions",
      align: "center",
      width: "275px",
      sortable: false
    }
  ]);

  const headersVersao = ref<TableHeader[]>([
    {
      title: "Versão",
      value: "versao",
      align: "center",
      width: "100px",
      sortable: false
    },
    {
      title: "Data Preenchimento",
      value: "data_preenchimento",
      align: "center",
      sortable: true
    },
    {
      title: "Usuário",
      value: "usuario.name",
      align: "center",
      sortable: true
    },
    {
      title: "Ações",
      value: "actions",
      align: "center",
      width: "245px",
      sortable: false
    }
  ]);

  const headersHistorico = ref<TableHeader[]>([
    {
      title: "Usuário",
      value: "usuario.name",
      align: "center",
      width: "200px",
      sortable: false
    },
    {
      title: "Ação",
      value: "acao_texto",
      align: "center",
      sortable: false
    },
    {
      title: "Data",
      value: "data_acao",
      align: "center",
      width: "20%",
      sortable: false
    }
  ]);

  // Computed properties
  const analiseId = computed(() => {
    return Number(route.params?.analiseId) || null;
  });

  const formularioId = computed(() => {
    return Number(route.params?.formularioId) || null;
  });

  const autosaveUrl = computed((): string => {
    return `/sapc/analise/${analiseId.value}/formulario/${formularioId.value}/auto-save`;
  });

  const autosaveData = computed((): string => {
    return analiseFormulario.value.texto_auto_save || "";
  });

  const isFinished = computed((): boolean => {
    return analiseFormulario.value.status === "finalizado";
  });

  const hasSomeRemessaInadimplente = computed(
    (): boolean => analiseStore.hasSomeRemessaInadimplente
  );

  const hasRemessas = computed((): boolean => analiseStore.hasRemessas);

  const hasAnalise = computed((): boolean => {
    return analiseStore.analise ? true : false;
  });

  // Métodos
  const updateAnexos = (pagination: any): void => {
    dataTableAnexo.pagination = pagination;

    if (!isAnexosFetched.value) {
      return;
    }

    fetchAnexos();
  };

  const updateVersoes = (pagination: any): void => {
    dataTableVersao.pagination = pagination;
    fetchVersoes();
  };

  const updateHistorico = (pagination: any): void => {
    dataTableHistorico.pagination = pagination;
    fetchHistorico();
  };

  const fetchRemessas = async (): Promise<void> => {
    try {
      if (!hasRemessas.value) await analiseStore.fetchRemessas(analiseId.value);
    } catch (error) {
      console.error(error);
      handleError(error);
    }
  };

  const fetchAnalise = async (): Promise<void> => {
    try {
      if (!hasAnalise.value) await analiseStore.fetchAnalise(analiseId.value);
    } catch (error) {
      console.error(error);
      handleError(error);
    }
  };

  const showRemessaInadimplenteAlert = (): void => {
    isRemessaInadimplenteAlertVisible.value = true;
  };

  const backToAnalises = (): void => {
    router.push("/e-contas/analises");
  };

  const loadPage = async (): Promise<void> => {
    if (!analiseId.value) {
      backToAnalises();
      return;
    }

    fetchStore.setFetchState("fetching");
    isLoadingPage.value = true;

    try {
      await fetchData();
      await fetchAnexos();
      await fetchVersoes();
      await fetchHistorico();
      fetchStore.setFetchState("done");
      isLoadingPage.value = false;
    } catch (error) {
      fetchStore.setFetchState("error");
      isLoadingPage.value = false;
      console.error(error);
    }
  };

  const verFormularioVersao = (formularioVersaoId: number): void => {
    router.push(
      `/e-contas/analise/${analiseId.value}/formulario/${formularioId.value}/versao/${formularioVersaoId}`
    );
  };

  const fetchData = async (): Promise<void> => {
    loading.value = true;
    try {
      const objAnalise = await SAPCApi.fetchAnaliseFormulario(
        analiseId.value,
        formularioId.value
      );
      loading.value = false;
      analiseFormulario.value = objAnalise.data.data;
      texto.value = analiseFormulario.value.texto || "";
    } catch (error) {
      loading.value = false;
      console.error(error);
    }
  };

  const fetchAnexos = async (): Promise<void> => {
    loadingAnexos.value = true;
    processing.value = true;

    const filterAnexo = {
      analiseId: analiseId.value,
      formularioId: formularioId.value
    };

    try {
      const success = await SAPCApi.fetchAnexos(
        dataTableAnexo.pagination,
        filterAnexo
      );

      isAnexosFetched.value = true;
      listaAnexos.value = success.data.data;
      loadingAnexos.value = false;
      processing.value = false;
      const meta = success.data;
      if (!meta) return;
      dataTableAnexo.pagination.page = meta.current_page;
      dataTableAnexo.pagination.itemsPerPage = parseInt(meta.per_page);
      dataTableAnexo.totalItems = meta.total;
    } catch (error) {
      loadingAnexos.value = false;
      processing.value = false;
      handleError(error);
      console.error(error);
    }
  };

  const fetchVersoes = async (): Promise<void> => {
    loadingVersoes.value = true;

    const filterVersao = {
      analiseId: analiseId.value,
      formularioId: formularioId.value
    };

    try {
      const success = await SAPCApi.fetchVersoes(
        dataTableVersao.pagination,
        filterVersao
      );

      listaVersoes.value = success.data.data;
      loadingVersoes.value = false;
      const meta = success.data;
      if (!meta) return;
      dataTableVersao.pagination.page = meta.current_page;
      dataTableVersao.pagination.itemsPerPage = parseInt(meta.per_page);
      dataTableVersao.totalItems = meta.total;
    } catch (error) {
      loadingVersoes.value = false;
      handleError(error);
      console.error(error);
    }
  };

  const fetchHistorico = async (): Promise<void> => {
    loadingHistorico.value = true;

    const filterHistorico = {
      analiseId: analiseId.value,
      formularioId: formularioId.value
    };

    try {
      const success = await SAPCApi.fetchHistorico(
        dataTableHistorico.pagination,
        filterHistorico
      );

      historico.value = success.data.data;
      const meta = success.data;
      if (!meta) return;
      dataTableHistorico.pagination.page = meta.current_page;
      dataTableHistorico.pagination.itemsPerPage = parseInt(meta.per_page);
      dataTableHistorico.totalItems = meta.total;
      loadingHistorico.value = false;
    } catch (error) {
      loadingHistorico.value = false;
      handleError(error);
      console.error(error);
    }
  };

  const save = (): void => {
    const analiseFormularioParam = {
      formularioId: formularioId.value,
      texto: texto.value,
      status: "iniciado"
    };

    processing.value = true;
    editor.isSaving = true;

    SAPCApi.saveAnaliseFormulario(analiseFormularioParam)
      .then(() => {
        Toaster.toast({
          message: "Formulário salvo com sucesso!",
          status: "success"
        });
        menuSapcStore.fetchMenu(analiseId.value as number); // Usado com o template TDefault
        loadPage();
        loading.value = false;
        processing.value = false;
        editor.isSaving = false;
      })
      .catch((error) => {
        Toaster.toast({
          message: error.data.message
            ? error.data.message
            : error.response.data.message,
          status: "warn"
        });
        processing.value = false;
        editor.isSaving = false;
      });
  };

  const saveAndFinish = (): void => {
    const analiseFormularioParam = {
      formularioId: formularioId.value,
      texto: texto.value,
      status: "finalizado"
    };

    processing.value = true;
    editor.isSaving = true;

    SAPCApi.saveAnaliseFormulario(analiseFormularioParam)
      .then(() => {
        Toaster.toast({
          message: "Formulário salvo com sucesso!",
          status: "success"
        });
        menuSapcStore.fetchMenu(analiseId.value as number); // Usado com o template TDefault
        loadPage();
        loading.value = false;
        processing.value = false;
        editor.isSaving = false;
      })
      .catch((error) => {
        Toaster.toast({
          message: error.data.message
            ? error.data.message
            : error.response.data.message,
          status: "warn"
        });
        processing.value = false;
        editor.isSaving = false;
      });
  };

  const reOpenForm = (): void => {
    const analiseFormularioParam = {
      analiseId: analiseId.value,
      formularioId: formularioId.value
    };

    processing.value = true;

    SAPCApi.reabrirAnaliseFormulario(analiseFormularioParam)
      .then(() => {
        Toaster.toast({
          message: "Formulário reaberto com sucesso!",
          status: "success"
        });
        menuSapcStore.fetchMenu(analiseId.value as number); // Usado com o template TDefault
        loadPage();
        loading.value = false;
        processing.value = false;
      })
      .catch((error) => {
        Toaster.toast({
          message: error.data.message ?? error.response.data.message,
          status: "warn"
        });
        processing.value = false;
      });
  };

  const downloadAnexo = (
    analiseFormularioId: number,
    anexoId: number
  ): void => {
    SAPCApi.downloadAnexo(analiseId.value, analiseFormularioId, anexoId);
  };

  const excluirAnexo = (anexo: Anexo) => {
    const answer = confirm(
      `Deseja excluir o anexo: ${anexo.descricao} - ${anexo.arquivo} ?`
    );

    if (!answer) return;

    SAPCApi.excluirAnexo(anexo.id)
      .then(() => {
        Toaster.toast({
          message: "Anexo excluído com sucesso!",
          status: "success"
        });
        loadPage();
      })
      .catch(handleError);
  };

  const handleError = (err: any): void => {
    const error = err.message ?? err;
    if (error) {
      Toaster.toast({
        message: err.message,
        status: "warn"
      });
    }
  };

  const formatDate = (value: string): string => {
    const d = new Date(value);
    return d.toLocaleString("pt-BR").substr(0, 10);
  };

  const formatDateTime = (value: string): string => {
    const d = new Date(value);
    return d.toLocaleString("pt-BR");
  };

  const handleEditorStatusChange = (status: EditorStatus): void => {
    editor.status = status;
  };

  const handleEditorUpdateData = (data: string): void => {
    texto.value = data;
  };

  const handleEditorReady = () => {
    if (hasRemessas.value && hasSomeRemessaInadimplente.value) {
      showRemessaInadimplenteAlert();
    } else {
      loadPage();
    }
  };

  const handleModalAttachmentClose = async ({
    file,
    fileDescription
  }: {
    file: File;
    fileDescription: string;
  }) => {
    if (!file || !fileDescription) {
      return;
    }

    try {
      await SAPCApi.uploadAnexo(
        analiseFormulario.value.analise_id,
        analiseFormulario.value.id,
        file,
        fileDescription
      );
    } catch (error) {
      console.error(error);
    } finally {
      modalAttachmentIsVisible.value = false;
      fetchAnexos();
      fetchVersoes();
      fetchHistorico();
    }
  };

  await fetchAnalise();
  await fetchRemessas();

  if (!hasRemessas.value) router.replace({ name: "analises" });

  watch(
    () => route.params.formularioId,
    async () => {
      await nextTick();
      if (hasRemessas.value && !hasSomeRemessaInadimplente.value) {
        loadPage();
      }
    }
  );
</script>
