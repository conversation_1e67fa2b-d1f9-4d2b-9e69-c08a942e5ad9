export interface AnaliseFormulario {
  id: string;
  nome: string;
  analiseId: string;
  versao?: string;
  texto?: string;
  texto_auto_save?: string;
  status?: string;
  user_finalizado?: string;
}

export interface Anexo {
  id: number;
  analise_formulario_id: number;
  arquivo: string;
  descricao: string;
  usuario: {
    name: string;
  };
  created_at: string;
}

export interface FormularioVersao {
  id: number;
  versao: string;
  data_preenchimento: string;
  usuario: {
    name: string;
  };
}

export interface HistoricoItem {
  id: number;
  usuario: {
    name: string;
  };
  acao_texto: string;
  data_acao: string;
}

export interface DataTableOptions {
  page: number;
  itemsPerPage: number;
}

export interface DataTableState {
  totalItems: number;
  pagination: DataTableOptions;
}

export interface EditorStatus {
  isBusy: boolean;
  isDirty?: boolean;
}

export interface EditorState {
  status: EditorStatus;
  isSaving: boolean;
}

export interface TableHeader {
  title: string;
  value: string;
  align?: string;
  sortable?: boolean;
  width?: string;
}
