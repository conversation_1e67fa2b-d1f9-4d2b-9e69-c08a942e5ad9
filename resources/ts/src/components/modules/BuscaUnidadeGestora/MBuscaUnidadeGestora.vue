<template>
  <AiStackLayout>
    <AiContainer>
      <AiContainerHeader>
        <AiContainerTitle> Filtrar </AiContainerTitle>
      </AiContainerHeader>

      <AiContainerContent>
        <SkeletonTable v-if="loadingFilterOptions" />

        <div
          v-else
          class="ai-grid-rows[repeat(2,auto)] ai-grid ai-grid-cols-4 ai-items-start ai-gap-x-4 ai-gap-y-8"
        >
          <AiCombobox
            v-model="filter.poder"
            :options="options.poderes"
            option-value="value"
            option-label="title"
            multiple
            label="Poder"
          />
          <AiCombobox
            v-model="filter.esfera"
            :options="options.esferas"
            option-value="value"
            option-label="title"
            multiple
            label="Esfera"
          />
          <AiCombobox
            v-model="filter.tipoUG"
            :options="options.tiposUG"
            option-value="value"
            option-label="title"
            multiple
            label="Tipo UG"
          />
          <AiCombobox
            v-model="filter.municipio"
            :options="options.municipios"
            option-value="value"
            option-label="text"
            multiple
            label="Municipio"
          />

          <div class="ai-col-span-4">
            <AiCombobox
              v-model="filter.unidadeGestora"
              :options="options.unidadesGestoras"
              option-value="value"
              option-label="text"
              multiple
              label="Unidade Gestora"
            />
          </div>
        </div>
      </AiContainerContent>

      <AiContainerFooter>
        <AiButton
          variant="outlined"
          :disabled="removingFilters || loading"
          @click="clearFilter"
        >
          <AiIcon>
            <AiSpinner v-if="removingFilters" />
            <IconSolarEraserOutline v-else />
          </AiIcon>
          Limpar Filtros
        </AiButton>

        <AiButton :disabled="applyingFilters || loading" @click="applyFilter">
          <AiIcon>
            <AiSpinner v-if="applyingFilters" />
            <IconSolarFilterOutline v-else />
          </AiIcon>
          Aplicar Filtro
        </AiButton>
      </AiContainerFooter>
    </AiContainer>

    <AiContainer>
      <AiContainerHeader>
        <AiContainerTitle> Unidades Gestoras </AiContainerTitle>
      </AiContainerHeader>

      <SkeletonTable v-if="firstRender" />

      <AiContainerContent v-else>
        <AiDataTable
          :columns="columns"
          :data="unidades_gestoras"
          :page-count="tableData.pageCount"
          :total-count="tableData.totalItems"
          :page-size="tableData.pagination.itemsPerPage"
          :current-page="tableData.pagination.page"
          @update:pagination="handleUpdatePagination"
        />
      </AiContainerContent>
    </AiContainer>
  </AiStackLayout>
</template>

<script setup lang="ts">
  import { ref, watch } from "vue";
  import Toaster from "@/components/Toaster.vue";
  import UnidadeGestoraApi from "@/api/auditor/managing_unit.js";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { useRouter } from "vue-router";
  import IconEye from "~icons/solar/eye-outline";

  interface Filter {
    poder: string[];
    esfera: string[];
    tipoUG: string[];
    municipio: string[];
    unidadeGestora: string[];
  }

  interface Options {
    poderes: any[];
    esferas: any[];
    tiposUG: any[];
    municipios: any[];
    unidadesGestoras: any[];
  }

  const router = useRouter();

  const filter = ref<Filter>({
    poder: [],
    esfera: [],
    tipoUG: [],
    municipio: [],
    unidadeGestora: []
  });

  const options = ref<Options>({
    poderes: [],
    esferas: [],
    tiposUG: [],
    municipios: [],
    unidadesGestoras: []
  });

  const columns = [
    { title: "Nome", key: "nome_com_cidade", align: "start" },
    { title: "Tipo", key: "tipo_unidade", align: "center" },
    { title: "Municipio", key: "municipio", align: "center" },
    {
      title: "Ações",
      id: "actions",
      actions: [
        {
          label: "Ver",
          icon: IconEye,
          action: (row: any) => {
            openLink(row.original.id);
          }
        }
      ]
    }
  ];

  const tableData = ref({
    totalItems: 0,
    pageCount: 1,
    pagination: {
      page: 1,
      itemsPerPage: 25
    }
  });

  const unidades_gestoras = ref<Array<any>>([]);
  const loading = ref(false);
  const firstRender = ref(true);
  const loadingFilterOptions = ref(false);
  const applyingFilters = ref(false);
  const removingFilters = ref(false);

  const fetchFilters = async () => {
    loadingFilterOptions.value = true;

    try {
      const { data } = await UnidadeGestoraApi.fetchFilters({
        poderes: true,
        esferas: true,
        tiposUG: true,
        municipios: true,
        auditorUGs: true
      });

      Object.keys(data.data).forEach((fieldOptions) => {
        options.value[fieldOptions as keyof Options] = data.data[fieldOptions];
      });
    } catch (error) {
      handleError(error);
    } finally {
      loadingFilterOptions.value = false;
    }
  };

  const fetchData = async () => {
    useFetchStore().setFetchState("fetching");
    loading.value = true;

    try {
      const response = await UnidadeGestoraApi.fetchUnidadesGestoras(
        tableData.value.pagination,
        filter.value
      );
      unidades_gestoras.value = response.data.data;
      const meta = response.data.meta;
      tableData.value.pagination.page = meta.current_page;
      tableData.value.pagination.itemsPerPage = parseInt(meta.per_page);
      tableData.value.totalItems = meta.total;
      tableData.value.pageCount = meta.last_page;
      loading.value = false;
      firstRender.value = false;
      useFetchStore().setFetchState("done");
    } catch (error) {
      handleError(error);
    }
  };

  const handleUpdatePagination = (
    newCurrentPage: number,
    newPageSize: number
  ) => {
    tableData.value.pagination.page = newCurrentPage;
    tableData.value.pagination.itemsPerPage = newPageSize;
    fetchData();
  };

  const openLink = (id: number) => {
    const routeData = router.resolve({
      name: "remessas",
      params: {
        unidadeGestoraId: id,
        exercicio: new Date().getFullYear()
      }
    });
    window.open(routeData.href, "_blank");
  };

  const handleError = (err: any) => {
    useFetchStore().setFetchState("error");
    loading.value = false;
    applyingFilters.value = false;
    removingFilters.value = false;
    loadingFilterOptions.value = false;
    firstRender.value = false;

    if (err.message) {
      Toaster.toast({
        message: err.message,
        status: "warn"
      });
    }
  };

  const clearFilter = async () => {
    filter.value = {
      poder: [],
      esfera: [],
      tipoUG: [],
      municipio: [],
      unidadeGestora: []
    };
  };

  const applyFilter = async () => {
    applyingFilters.value = true;

    await fetchData();

    applyingFilters.value = false;
  };

  const handleChangeFilter = async (newFilter: Filter) => {
    tableData.value.pagination.page = 1;

    if (
      JSON.stringify(newFilter) ===
      JSON.stringify({
        poder: [],
        esfera: [],
        tipoUG: [],
        municipio: [],
        unidadeGestora: []
      })
    ) {
      removingFilters.value = true;
    } else {
      applyingFilters.value = true;
    }

    await fetchData();

    removingFilters.value = false;
    applyingFilters.value = false;
  };

  watch(
    () => filter.value,
    (newFilter) => {
      handleChangeFilter(newFilter);
    },
    { deep: true }
  );

  fetchFilters();
  fetchData();
</script>
