<template>
  <AiContainerContent>
    <v-data-table-server
      :items-per-page="itemsPerPage"
      :headers="headers"
      :items="messages"
      :items-length="totalItems"
      item-value="id"
      :sort-by="filterRequestData.sortBy"
      no-data-text="Sem dados para exibir"
      @update:options="handleUpdateOptions"
      :multi-sort="true"
    >
      <template v-slot:top>
        <v-dialog v-model="dialogFinish" max-width="500px">
          <v-card>
            <v-card-title class="text-h5"
              >Tem certeza de que deseja finalizar a mensagem?</v-card-title
            >
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="blue-darken-1"
                variant="text"
                @click="closeDialogFinish()"
                >Não</v-btn
              >
              <v-btn
                color="blue-darken-1"
                variant="text"
                @click="handleConfirmFinish()"
                >Sim</v-btn
              >
              <v-spacer></v-spacer>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </template>

      <template v-slot:[`item.prioridade`]="{ item }">
        <v-tooltip bottom text="Tooltip">
          <template v-slot:activator="{ props }">
            <AiIcon
              v-bind="props"
              :icon="priorityIcon[item.prioridade]"
              width="20"
              :class="priorityColor[item.prioridade]"
            />
          </template>
          <span>{{ item.prioridadeValue }}</span>
        </v-tooltip>
      </template>

      <template v-slot:item.unidade_gestora_nome="{ item }">
        <span>{{ item.unidade_gestora_nome }} {{ item.cidade_nome }}</span>
      </template>

      <template v-slot:item.enviado_em="{ item }">
        <span>{{ new Date(item.enviado_em).toLocaleString() }}</span>
      </template>

      <template v-slot:[`item.actions`]="{ item }">
        <div class="ai-flex ai-gap-2">
          <template v-if="!item.mensagem_rascunho_id">
            <AiButton
              variant="outlined"
              size="sm"
              @click="handleViewButtonClick(item.id)"
            >
              <AiIcon><IconSolarEyeOutline /></AiIcon>
              Ver
            </AiButton>

            <AiButton
              size="sm"
              @click="handleDialogFinish(item.id)"
              v-if="isAuditor && item.status !== 'fechado'"
            >
              <AiIcon><IconSolarCheckCircleOutline /></AiIcon>
              Finalizar
            </AiButton>
          </template>

          <template v-else>
            <AiButton
              v-show="false"
              variant="outlined"
              size="sm"
              @click="handleRemoveDraftButtonClick(item.id)"
              disabled
            >
              <AiIcon><IconSolarTrashBinTrashOutline /></AiIcon>
              Remover
            </AiButton>

            <AiButton
              size="sm"
              @click="handleEditDraftButtonClick(item.id)"
              v-if="isAuditor"
            >
              <AiIcon><IconSolarDocumentAddOutline /></AiIcon>
              Editar
            </AiButton>
          </template>
        </div>
      </template>
    </v-data-table-server>
  </AiContainerContent>
</template>

<script lang="ts" setup>
  import { ref, watch, computed, inject } from "vue";
  import { useRouter } from "vue-router";

  import { useFetchMessagesJurisdicionado } from "@/composables/mensagens/useFetchMessagesJurisdicionado";
  import { useFinalizeMessage } from "@/composables/mensagens/useFinalizeMessage";
  import { useFilterResquestData } from "@/composables/mensagens/useFilterResquestData";

  import IconPriorityLow from "~icons/solar/round-double-alt-arrow-down-bold";
  import IconPriorityMedium from "~icons/solar/minus-circle-bold";
  import IconPriorityHigh from "~icons/solar/round-double-alt-arrow-up-bold";
  import IconPriorityUrgent from "~icons/solar/danger-circle-bold";

  import { useAuthStore } from "@/stores/auditor/authStore";
  type Message = {
    id: number;
    prioridade: string;
    unidade_gestora_id: number;
    municipio: string;
    assunto: string;
    status: string;
    tipo_mensagem_id: number;
    enviado_em: string;
  };

  const emit = defineEmits<{
    "update:options": [options: any];
    "action:view": [];
    "action:finish": [];
  }>();

  const router = useRouter();
  const timer = ref<number | null>(null);

  const { filterRequestData } = useFilterResquestData();

  const headers = [
    { title: "!", key: "prioridade" },
    { title: "No.", key: "id", value: "id" },
    { title: "Unidade Gestora", key: "unidade_gestora_nome" },
    { title: "Data", key: "enviado_em", value: "enviado_em" },
    { title: "Tipo de Mensagem", key: "tipo_mensagem_nome" },
    { title: "Assunto", key: "assunto" },
    { title: "Situação", key: "status" },
    { title: "Remetente", key: "user_name" },
    { title: "Ações", key: "actions", sortable: false }
  ];

  const dialogFinish = ref(false);
  const finishId = ref(0);

  const itemsPerPage = 10;

  const messages = ref<Message[]>([]);
  const totalItems = ref<number>(messages.value.length);

  const handleUpdateOptions = async ({ page, itemsPerPage, sortBy }) => {
    filterRequestData.value.page = page;
    filterRequestData.value.itemsPerPage = itemsPerPage;
    filterRequestData.value.sortBy = sortBy;

    const { data } = await useFetchMessagesJurisdicionado(
      filterRequestData.value
    );

    if (data.value) {
      messages.value = data.value.data;
      totalItems.value = data.value.total;
    }
  };

  const handleViewButtonClick = (messageId: number) => {
    router.push(`/mensagens/ver-mensagem/${messageId}`);
  };

  const handleDialogFinish = async (id: number) => {
    emit("action:finish");
    dialogFinish.value = true;
    finishId.value = id;
  };

  const closeDialogFinish = () => {
    dialogFinish.value = false;
  };

  const handleEditDraftButtonClick = (id: number) => {
    router.push(`/mensagens/editar-mensagem/${id}`);
  };

  const handleRemoveDraftButtonClick = (id: number) => {
    console.log("Remove button clicked" + id);
  };

  watch(filterRequestData, () => {
    clearTimeout(timer.value);

    timer.value = setTimeout(() => {
      handleUpdateOptions({
        page: filterRequestData.value.page,
        itemsPerPage: filterRequestData.value.itemsPerPage,
        sortBy: filterRequestData.value.sortBy
      });
    }, 100);
  });

  const handleConfirmFinish = async () => {
    if (!finishId.value) return;
    const { error } = await useFinalizeMessage(finishId.value);

    error?.value && console.error(error.value);

    handleUpdateOptions({
      page: filterRequestData.value.page,
      itemsPerPage: filterRequestData.value.itemsPerPage,
      sortBy: filterRequestData.value.sortBy
    });
    closeDialogFinish();
  };

  const priorityIcon = {
    4: IconPriorityLow,
    3: IconPriorityMedium,
    2: IconPriorityHigh,
    1: IconPriorityUrgent
  };

  const priorityColor = {
    4: "ai-text-priority-low",
    3: "ai-text-priority-medium",
    2: "ai-text-priority-high",
    1: "ai-text-priority-urgent"
  };

  const auth = inject("auth");

  const authStore = useAuthStore();
  const { user } = authStore;

  const isAuditor = computed(() => {
    const roleFromAuth = auth?.$vm?.state?.data?.role; //jur
    const roleFromUser = user?.data?.role; //auditor
    return roleFromAuth === "Auditor" || roleFromUser === "Auditor";
  });
</script>
