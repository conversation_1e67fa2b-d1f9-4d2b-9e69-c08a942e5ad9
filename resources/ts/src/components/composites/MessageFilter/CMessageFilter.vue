<template>
  <AiContainerHeader>
    <AiContainerTitle>Filtrar</AiContainerTitle>
  </AiContainerHeader>

  <AiContainerContent>
    <div
      class="ai-grid-rows[repeat(3,auto)] ai-grid ai-grid-cols-5 ai-items-start ai-gap-x-4 ai-gap-y-2"
    >
      <v-combobox
        v-model="filter.messageNumber"
        label="Número da mensagem"
        clearable
        density="compact"
        variant="outlined"
        multiple
        chips
      />

      <v-autocomplete
        v-model="filter.messagePriority"
        :items="priorities"
        label="Prioridade"
        multiple
        small-chips
        clearable
        density="compact"
        variant="outlined"
        item-title="value"
        item-value="key"
      />

      <v-autocomplete
        v-model="filter.messageSituation"
        :items="statuses"
        label="Situação"
        multiple
        small-chips
        clearable
        density="compact"
        variant="outlined"
        item-title="value"
        item-value="key"
      />

      <v-autocomplete
        v-model="filter.ugType"
        :items="options.tiposUG"
        label="Tipo UG"
        multiple
        small-chips
        clearable
        density="compact"
        variant="outlined"
        item-title="text"
        item-value="value"
      />

      <v-switch
        v-if="isAuditor"
        v-model="filter.showDrafts"
        label="Mostrar rascunhos"
        density="compact"
        color="primary"
        inset
      />

      <div class="ai-col-span-2">
        <v-autocomplete
          v-model="filter.city"
          :items="options.municipios"
          label="Município"
          multiple
          chips
          clearable
          density="compact"
          variant="outlined"
          item-title="text"
          item-value="value"
        />
      </div>

      <v-autocomplete
        v-model="filter.diretoria"
        :items="diretorias"
        label="Diretoria"
        clearable
        multiple
        small-chips
        density="compact"
        variant="outlined"
        item-title="text"
        item-value="value"
      />

      <div class="ai-col-span-2">
        <v-autocomplete
          v-model="filter.ug"
          :items="options.unidadesGestoras"
          label="Unidade Gestora"
          multiple
          chips
          clearable
          density="compact"
          variant="outlined"
          item-title="text"
          item-value="value"
        />
      </div>

      <div class="ai-col-span-2">
        <v-text-field
          v-model="filter.messageSubject"
          label="Assunto"
          clearable
          density="compact"
          variant="outlined"
        />
      </div>

      <v-text-field
        v-model="filter.message"
        label="Conteúdo da mensagem"
        clearable
        density="compact"
        variant="outlined"
      />

      <div class="ai-col-span-2 ai-grid ai-grid-cols-2 ai-gap-4">
        <Date
          :value="filter.initialDate"
          label="Prazo de entrega"
          density="compact"
          variant="outlined"
          @input-date="handleInitialDateInput"
        />

        <Date
          :value="filter.finalDate"
          label="Até"
          density="compact"
          variant="outlined"
          @input-date="handleFinalDateInput"
        />
      </div>
    </div>
  </AiContainerContent>

  <AiContainerFooter>
    <AiButton variant="outlined" @click="handleClearFilterButtonClick">
      <AiIcon><IconMdiEraser /></AiIcon>
      Limpar
    </AiButton>
    <AiButton @click="handleApplyFilterButtonClick">
      <AiIcon><IconMdiFilter /></AiIcon>
      Aplicar filtro
    </AiButton>
  </AiContainerFooter>
</template>

<script lang="ts" setup>
  import { useFetchPriorities } from "@/composables/mensagens/useFetchPriorities";
  import { useFetchMessageStatus } from "@/composables/mensagens/useFetchMessageStatus";
  import { useFetchFilterOptions } from "@/composables/global/useFetchFilterOptions";
  import { useFetchBoards } from "@/composables/mensagens/useFetchBoards";

  import { useMessageStore } from "@/stores/message/messageStore";
  import { ref, watch, inject, computed } from "vue";

  import { useAuthStore } from "@/stores/auditor/authStore";

  const messageStore = useMessageStore();

  const filter = ref({
    messageNumber: null,
    messagePriority: null,
    messageSituation: null,
    ugType: null,
    ug: null,
    diretoria: null,
    messageSubject: null,
    city: null,
    initialDate: null,
    finalDate: null,
    showDrafts: false,
    message: null
  });

  const params = {
    poderes: true,
    esferas: true,
    tiposUG: true,
    municipios: true,
    tipoAnalise: true,
    auditorUGs: true
  };

  filter.value = messageStore.filter;

  watch(
    filter,
    () => {
      messageStore.setFilter(filter.value);
    },
    { deep: true }
  );

  const handleApplyFilterButtonClick = () => {
    messageStore.setFilter(filter.value);
  };

  const handleInitialDateInput = (value: string) => {
    filter.value.initialDate = value;
  };

  const handleFinalDateInput = (value: string) => {
    filter.value.finalDate = value;
  };

  const handleClearFilterButtonClick = () => {
    filter.value = {
      messageNumber: null,
      messagePriority: null,
      messageSituation: null,
      ugType: null,
      ug: null,
      diretoria: null,
      messageSubject: null,
      city: null,
      initialDate: null,
      finalDate: null,
      showDrafts: false,
      message: null
    };
  };

  const { data: priorities, error: prioritiesError } =
    await useFetchPriorities();

  const { data: statuses, error: statusesError } =
    await useFetchMessageStatus();

  const { data: diretorias, error: diretoriasError } = await useFetchBoards();

  // ToDo: Separar requisições a esse conjunto de dados
  // ToDo: Mover requisições globais para Mödulo Global
  const {
    data: {
      value: { data: options }
    },
    error: optionsError
  } = await useFetchFilterOptions(params);

  const auth = inject("auth");
  const authStore = useAuthStore();
  const { user } = authStore;

  const isAuditor = computed(() => {
    const roleFromAuth = auth?.$vm?.state?.data?.role; //jur
    const roleFromUser = user?.data?.role; //auditor
    return roleFromAuth === "Auditor" || roleFromUser === "Auditor";
  });
  prioritiesError?.value && console.error(prioritiesError.value);
  statusesError?.value && console.error(statusesError.value);
  optionsError?.value && console.error(optionsError.value);
  diretoriasError?.value && console.error(diretoriasError.value);
</script>
