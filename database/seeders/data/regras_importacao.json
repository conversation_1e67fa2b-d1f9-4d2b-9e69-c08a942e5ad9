[{"layout": "PrevisaoReceita", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1020", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "Acao", "campo": "CodigoUnidadeGestora", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "numero_regra": "1030", "exercicio_inicial": "2022"}, {"layout": "AcaoInicial", "campo": "CodigoUnidadeGestora", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "numero_regra": "1030", "exercicio_inicial": "2025"}, {"layout": "Acao", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1031", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "Acao", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY_OR", "parametro": "UnidadeOrcamentaria,codigo,UnidadeOrcamentariaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente nos leiautes (UnidadeOrcamentaria) ou (UnidadeOrcamentariaInicial).", "impeditiva": true, "numero_regra": "1031", "exercicio_inicial": "2025"}, {"layout": "AcaoInicial", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentariaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentariaInicial).", "impeditiva": true, "numero_regra": "1031", "exercicio_inicial": "2025"}, {"layout": "Acao", "campo": "CodigoPrograma", "regra": "FOREIGN_KEY", "parametro": "Programa,codigo", "mensagem_erro": "É preciso que o campo (CodigoPrograma) tenha um registro correspondente nos leiautes (Programa).", "impeditiva": true, "numero_regra": "1034", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "Acao", "campo": "CodigoPrograma", "regra": "FOREIGN_KEY_OR", "parametro": "Programa,codigo,PorgramaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoPrograma) tenha um registro correspondente nos leiautes (Programa) ou (ProgramaInicial).", "impeditiva": true, "numero_regra": "1034", "exercicio_inicial": "2025"}, {"layout": "AcaoInicial", "campo": "CodigoPrograma", "regra": "FOREIGN_KEY", "parametro": "ProgramaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoPrograma) tenha um registro correspondente no leiaute (ProgramaInicial).", "impeditiva": true, "numero_regra": "1034", "exercicio_inicial": "2025"}, {"layout": "PlanoInterno", "campo": "NumeroAcao", "regra": "FOREIGN_KEY", "parametro": "Acao,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente no leiaute (Acao).", "impeditiva": true, "numero_regra": "1040", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "PlanoInternoInicial", "campo": "NumeroAcao", "regra": "FOREIGN_KEY", "parametro": "AcaoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente no leiaute (AcaoInicial).", "impeditiva": true, "numero_regra": "1040", "exercicio_inicial": "2025"}, {"layout": "PlanoInterno", "campo": "NumeroAcao", "regra": "FOREIGN_KEY_OR", "parametro": "Acao,numero,AcaoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente nos leiautes (Acao) ou (AcaoInicial).", "impeditiva": true, "numero_regra": "1040", "exercicio_inicial": "2025"}, {"layout": "Dotacao", "campo": "NumeroAcao", "regra": "FOREIGN_KEY", "parametro": "Acao,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente no leiaute (Acao).", "impeditiva": true, "numero_regra": "1050", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "Dotacao", "campo": "NumeroAcao", "regra": "FOREIGN_KEY_OR", "parametro": "Acao,numero,AcaoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente nos leiautes (Acao) ou (AcaoInicial).", "impeditiva": true, "numero_regra": "1050", "exercicio_inicial": "2025"}, {"layout": "DotacaoInicial", "campo": "NumeroAcao", "regra": "FOREIGN_KEY", "parametro": "AcaoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente no leiaute (AcaoInicial).", "impeditiva": true, "numero_regra": "1050", "exercicio_inicial": "2025"}, {"layout": "Dotacao", "campo": "NumeroPlanoInterno", "regra": "FOREIGN_KEY", "parametro": "PlanoInterno,numero", "mensagem_erro": "É preciso que o campo (NumeroPlanoInterno) tenha um registro correspondente no leiaute (PlanoInterno).", "impeditiva": true, "numero_regra": "1051", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "DotacaoInicial", "campo": "NumeroPlanoInterno", "regra": "FOREIGN_KEY", "parametro": "PlanoInternoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroPlanoInterno) tenha um registro correspondente no leiaute (PlanoInternoInicial).", "impeditiva": true, "numero_regra": "1051", "exercicio_inicial": "2025"}, {"layout": "Dotacao", "campo": "NumeroPlanoInterno", "regra": "FOREIGN_KEY_OR", "parametro": "PlanoInterno,numero,PlanoInternoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroPlanoInterno) tenha um registro correspondente nos leiautes (PlanoInterno) ou (PlanoInternoInicial).", "impeditiva": true, "numero_regra": "1051", "exercicio_inicial": "2025"}, {"layout": "Dotacao", "campo": "CodigoUnidadeGestora", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "numero_regra": "1052", "exercicio_inicial": "2022"}, {"layout": "DotacaoInicial", "campo": "CodigoUnidadeGestora", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "numero_regra": "1052", "exercicio_inicial": "2025"}, {"layout": "Dotacao", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1053", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "DotacaoInicial", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentariaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentariaInicial).", "impeditiva": true, "numero_regra": "1053", "exercicio_inicial": "2025"}, {"layout": "Dotacao", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY_OR", "parametro": "UnidadeOrcamentariaInicial,codigo,UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente nos leiautes (UnidadeOrcamentariaInicial) ou (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1053", "exercicio_inicial": "2025"}, {"layout": "Dotacao", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": false, "numero_regra": "1054", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "Dotacao", "campo": "CodigoFonteRecursoProprio", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,codigo_principal", "mensagem_erro": "Se não existir correspondente do campo (CodigoFonteRecursoProprio) definidos no leiaute (FonteRecursoProprio).", "impeditiva": true, "numero_regra": "1055", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "Dotacao", "campo": "CodigoFonteRecursoProprio", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecursoProprio", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022. Esta regra será impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "1056", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "Dotacao", "campo": "CodigoFonteRecurso", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecurso", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022.", "impeditiva": true, "numero_regra": "1056", "exercicio_inicial": "2025"}, {"layout": "DotacaoInicial", "campo": "CodigoFonteRecurso", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecurso", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022.", "impeditiva": true, "numero_regra": "1056", "exercicio_inicial": "2025"}, {"layout": "AnulacaoDotacao", "campo": "NumeroAcao", "regra": "FOREIGN_KEY", "parametro": "Acao,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente no leiaute (Acao).", "impeditiva": true, "numero_regra": "1060", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "AnulacaoDotacao", "campo": "NumeroAcao", "regra": "FOREIGN_KEY_OR", "parametro": "Acao,numero,AcaoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente nos leiautes (Acao) ou (AcaoInicial).", "impeditiva": true, "numero_regra": "1060", "exercicio_inicial": "2025"}, {"layout": "AnulacaoDotacao", "campo": "NumeroPlanoInterno", "regra": "FOREIGN_KEY", "parametro": "PlanoInterno,numero", "mensagem_erro": "É preciso que o campo (NumeroPlanoInterno) tenha um registro correspondente no leiaute (PlanoInterno).", "impeditiva": true, "numero_regra": "1061", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "AnulacaoDotacao", "campo": "NumeroPlanoInterno", "regra": "FOREIGN_KEY_OR", "parametro": "PlanoInterno,numero,PlanoInternoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroPlanoInterno) tenha um registro correspondente nos leiautes (PlanoInterno) ou (PlanoInternoInicial).", "impeditiva": true, "numero_regra": "1061", "exercicio_inicial": "2025"}, {"layout": "AnulacaoDotacao", "campo": "CodigoUnidadeGestora", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "numero_regra": "1062", "exercicio_inicial": "2022"}, {"layout": "AnulacaoDotacao", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1063", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "AnulacaoDotacao", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY_OR", "parametro": "UnidadeOrcamentaria,codigo,UnidadeOrcamentariaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria) ou (UnidadeOrcamentariaInicial).", "impeditiva": true, "numero_regra": "1063", "exercicio_inicial": "2025"}, {"layout": "AnulacaoDotacao", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1064", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "AnulacaoDotacao", "campo": "CodigoFonteRecursoProprio", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,codigo_principal", "mensagem_erro": "É preciso que o campo (Codigofonterecursoproprio) tenha um registro correspondente no leiaute (FonteRecursoProprio).", "impeditiva": true, "numero_regra": "1065", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "AnulacaoDotacao", "campo": "CodigoFonteRecursoProprio", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecursoProprio", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022. Esta regra será impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "1066", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "AnulacaoDotacao", "campo": "CodigoFonteRecurso", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecurso", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022", "impeditiva": true, "numero_regra": "1066", "exercicio_inicial": "2025"}, {"layout": "CreditoAdicional", "campo": "NumeroPlanoInterno", "numero_regra": "1070", "regra": "FOREIGN_KEY", "parametro": "PlanoInterno,numero", "mensagem_erro": "É preciso que o campo (NumeroPlanoInterno) tenha um registro correspondente no leiaute (PlanoInterno).", "impeditiva": true, "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "CreditoAdicional", "campo": "NumeroPlanoInterno", "numero_regra": "1070", "regra": "FOREIGN_KEY_OR", "parametro": "PlanoInterno,numero,PlanoInternoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroPlanoInterno) tenha um registro correspondente nos leiautes (PlanoInterno) ou (PlanoInternoInicial).", "impeditiva": true, "exercicio_inicial": "2025"}, {"layout": "CreditoAdicional", "campo": "CodigoUnidadeGestora", "numero_regra": "1071", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "CreditoAdicional", "campo": "CodigoUnidadeOrcamentaria", "numero_regra": "1072", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "CreditoAdicional", "campo": "CodigoUnidadeOrcamentaria", "numero_regra": "1072", "regra": "FOREIGN_KEY_OR", "parametro": "UnidadeOrcamentaria,codigo,UnidadeOrcamentariaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente nos leiautes (UnidadeOrcamentaria) ou (UnidadeOrcamentariaInicial).", "impeditiva": true, "exercicio_inicial": "2025"}, {"layout": "CreditoAdicional", "campo": "CodigoFonteRecursoProprio", "numero_regra": "1073", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,codigo_principal", "mensagem_erro": "É preciso que o campo (CodigoFonteRecursoProprio) tenha um registro correspondente no leiaute (FonteRecursoProprio).", "impeditiva": true, "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "CreditoAdicional", "campo": "CodigoFonteRecursoProprio", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecursoProprio", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022. Esta regra será impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "1074", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "CreditoAdicional", "campo": "CodigoFonteRecurso", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecurso", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022.", "impeditiva": true, "numero_regra": "1074", "exercicio_inicial": "2025"}, {"layout": "ListaFornecedor", "campo": "CodigoUnidadeGestora", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "numero_regra": "1100", "exercicio_inicial": "2022"}, {"layout": "ListaFornecedor", "campo": "CodigoFornecedor", "regra": "FOREIGN_KEY", "parametro": "Fornecedor,codigo", "mensagem_erro": "É preciso que o campo (CodigoFornecedor) tenha um registro correspondente no leiaute (Fornecedor).", "impeditiva": true, "numero_regra": "1101", "exercicio_inicial": "2022"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "CodigoFonteRecursoProprio", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,codigo_principal", "mensagem_erro": "É preciso que o campo (CodigoFonteRecursoProprio) tenha um registro correspondente no leiaute (FonteRecursoProprio)", "impeditiva": true, "numero_regra": "1121", "exercicio_inicial": "2022"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "CodigoFonteRecursoProprio", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecursoProprio", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022. Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "1125", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "CodigoFonteRecurso", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecurso", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022.", "impeditiva": true, "numero_regra": "1125", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoUnidadeGestora", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "numero_regra": "1130", "exercicio_inicial": "2022"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1131", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY_OR", "parametro": "UnidadeOrcamentaria,codigo,UnidadeOrcamentariaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente nos leiautes (UnidadeOrcamentaria) ou (UnidadeOrcamentariaInicial).", "impeditiva": true, "numero_regra": "1131", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoPrograma", "regra": "FOREIGN_KEY", "parametro": "Programa,codigo", "mensagem_erro": "É preciso que o campo (CodigoPrograma) tenha um registro correspondente no leiaute (Programa).", "impeditiva": true, "numero_regra": "1133", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NumeroAcao", "regra": "FOREIGN_KEY", "parametro": "Acao,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente no leiaute (Acao).", "impeditiva": true, "numero_regra": "1134", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NumeroAcao", "regra": "FOREIGN_KEY_OR", "parametro": "Acao,numero,AcaoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente nos leiautes (Acao) ou (AcaoInicial).", "impeditiva": true, "numero_regra": "1134", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1135", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário ter um registro correspondente no leiaute (Licitacao).", "impeditiva": false, "numero_regra": "1136", "exercicio_inicial": "2022"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NumeroContrato", "regra": "FOREIGN_KEY", "parametro": "Contrato,numero_contrato", "mensagem_erro": "É preciso que o campo (NumeroContrato) tenha um registro correspondente no leiaute (Contrato).", "impeditiva": false, "numero_regra": "1137", "exercicio_inicial": "2022"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NumeroConvenio", "regra": "FOREIGN_KEY", "parametro": "Convenios,numero_convenio", "mensagem_erro": "É preciso que o campo (NumeroConvenio) tenha um registro correspondente no leiaute (Convenio).", "impeditiva": false, "numero_regra": "1138", "exercicio_inicial": "2022"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NumeroContratacao", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário ter um registro correspondente na tabela (ContratacaoDireta).", "impeditiva": false, "numero_regra": "1139", "exercicio_inicial": "2022"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "C<PERSON>r", "regra": "FOREIGN_KEY", "parametro": "Fornecedor,codigo", "mensagem_erro": "É preciso que o campo (Credor) tenha um registro correspondente no leiaute (Fornecedor).", "impeditiva": true, "numero_regra": "1140", "exercicio_inicial": "2022"}, {"layout": "Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1141", "exercicio_inicial": "2022"}, {"layout": "Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1142", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1143", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "", "regra": "?", "parametro": "Se (Tipo) no leiaute (Empenho) receber valores 2-Estimativo ou 3-Global", "mensagem_erro": "Não é possível reforçar Empenhor do tipo 1-Ordinário.", "impeditiva": true, "numero_regra": "1144", "exercicio_inicial": "2022"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "<PERSON><PERSON>", "regra": "EXERCICIO", "parametro": "-", "mensagem_erro": "O Ano do Empenho deve ser igual ao Exercício da Remessa.", "impeditiva": true, "numero_regra": "1145", "exercicio_inicial": "2024"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoFonteRecursoProprio", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecursoProprio", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022. Esta regra será impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "1147", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoFonteRecurso", "regra": "CODIGO_FONTE_RECURSO_PROPRIO", "parametro": "CodigoFonteRecurso", "mensagem_erro": "Código para Fonte ou Destinação de Recursos deve conter os 4 primeiros dígitos conforme o padrão definido pela Portaria STN 710 - 25/02/2021 e Portaria TCEAL 205/2022 - 12/08/2022.", "impeditiva": true, "numero_regra": "1147", "exercicio_inicial": "2025"}, {"layout": "AnulacaoEmpenho", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1150", "exercicio_inicial": "2022"}, {"layout": "AnulacaoEmpenho", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1151", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoEmpenho", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1152", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoEmpenho", "campo": "ValorAnulacao", "regra": "?", "parametro": "Se o valor do campo (ValorAnulacao), ou o somatório deste campo no caso de anulações parciais, for maior que o valor do campo (Valor) definido no leiaute Empenho.", "mensagem_erro": "O valor da(s) Anulação(ões) de Empenho são maiores que o7u valor do Empenho que está sendo anulado.", "impeditiva": true, "numero_regra": "1153", "exercicio_inicial": "2022"}, {"layout": "LiquidacaoEmpenho", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1160", "exercicio_inicial": "2022"}, {"layout": "LiquidacaoEmpenho", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1161", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "LiquidacaoEmpenho", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1162", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "LiquidacaoEmpenho", "campo": "Valor", "regra": "?", "parametro": "Se o valor do campo (Valor) definido no leiaute LiquidacaoEmpenho, ou o somatório da(s) liquidação(ões), subtraído o valor do campo (ValorAnulacao) definido no leiaute AnulacaoEmpenho, ou o somatório deste campo no caso de anulações parciais, for maior que o valor do campo (Valor) definido no leiaute Empenho.", "mensagem_erro": "O valor da(s) liquidação(ões) de empenho, subtraído a(s) anulação(ões) de empenho não pode ser maior que o valor total do empenho.", "impeditiva": true, "numero_regra": "1163", "exercicio_inicial": "2022"}, {"layout": "AnulacaoLiquidacaoEmpenho", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1164", "exercicio_inicial": "2022"}, {"layout": "AnulacaoLiquidacaoEmpenho", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1165", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoLiquidacaoEmpenho", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1166", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoLiquidacaoEmpenho", "campo": "Valor", "regra": "?", "parametro": "-", "mensagem_erro": "O valor excede o valor da Liquidação à qual se refere esta anulação.", "impeditiva": true, "numero_regra": "1167", "exercicio_inicial": "2022"}, {"layout": "AnulacaoLiquidacaoEmpenho", "campo": "NumeroLiquidacaoEmpenho", "regra": "FOREIGN_KEY", "parametro": "LiquidacaoEmpenho,numero", "mensagem_erro": "É preciso que o campo (NumeroLiquidacaoEmpenho) tenha um registro correspondente no leiaute (LiquidacaoEmpenho).", "impeditiva": true, "numero_regra": "1168", "exercicio_inicial": "2024"}, {"layout": "PagamentoEmpenho", "campo": "NumeroLiquidacaoEmpenho", "regra": "FOREIGN_KEY", "parametro": "LiquidacaoEmpenho,numero", "mensagem_erro": "É preciso que o campo (NumeroLiquidacaoEmpenho) tenha um registro correspondente no leiaute (LiquidacaoEmpenho) no campo (Numero).", "impeditiva": true, "numero_regra": "1169", "exercicio_inicial": "2025"}, {"layout": "PagamentoEmpenho", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1170", "exercicio_inicial": "2022"}, {"layout": "PagamentoEmpenho", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1171", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "PagamentoEmpenho", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1172", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "PagamentoEmpenho", "campo": "Valor", "regra": "?", "parametro": "Se caso o valor declaro no campo (Valor) for maior que o valor declarado no campo (Valor) no leiaute (LiquidacaoEmpenho).", "mensagem_erro": "O valor do (Valor) não pode ser maior que o (Valor) no leiaute (LiquidacaoEmpenho).", "impeditiva": true, "numero_regra": "1173", "exercicio_inicial": "2022"}, {"layout": "AnulacaoPagamentoEmpenho", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1174", "exercicio_inicial": "2022"}, {"layout": "AnulacaoPagamentoEmpenho", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1175", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoPagamentoEmpenho", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1176", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoPagamentoEmpenho", "campo": "Valor", "regra": "?", "parametro": "Se caso o valor declaro no campo (Valor) for maior que o valor declarado no campo (Valor) definido no leiaute PagamentoEmpenho.", "mensagem_erro": "Valor da anulação excede o valor do pagamento do empenho.", "impeditiva": true, "numero_regra": "1177", "exercicio_inicial": "2022"}, {"layout": "AnulacaoPagamentoEmpenho", "campo": "NumeroPagamentoEmpenho", "regra": "FOREIGN_KEY", "parametro": "PagamentoEmpenho,numero", "mensagem_erro": "É preciso que o campo (NumeroPagamentoEmpenho) tenha um registro correspondente no leiaute (PagamentoEmpenho).", "impeditiva": true, "numero_regra": "1178", "exercicio_inicial": "2024"}, {"layout": "RetencaoEmpenho", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1180", "exercicio_inicial": "2022"}, {"layout": "RetencaoEmpenho", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1181", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "RetencaoEmpenho", "campo": "NumeroDocumento", "regra": "FOREIGN_KEY", "parametro": "PagamentoEmpenho,numero", "mensagem_erro": "É preciso que o campo (NumeroDocumento) tenha um registro correspondente no leiaute (PagamentoEmpenho).", "impeditiva": true, "numero_regra": "1182", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "RetencaoEmpenho", "campo": "Valor", "regra": "?", "parametro": "-", "mensagem_erro": "O valor da(s) liquidação(ões) de empenho, subtraído a(s) anulação(ões) de empenho não pode ser maior que o valor total do empenho.", "impeditiva": true, "numero_regra": "1183", "exercicio_inicial": "2022"}, {"layout": "AnulacaoRetencaoEmpenho", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1184", "exercicio_inicial": "2022"}, {"layout": "AnulacaoRetencaoEmpenho", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1185", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoRetencaoEmpenho", "campo": "NumeroRetencaoEmpenho", "regra": "FOREIGN_KEY", "parametro": "RetencaoEmpenho,numero", "mensagem_erro": "É preciso que o campo (NumeroRetencaoEmpenho) tenha um registro correspondente no leiaute (RetencaoEmpenho).", "impeditiva": true, "numero_regra": "1186", "exercicio_inicial": "2022"}, {"layout": "AnulacaoRetencaoEmpenho", "campo": "Valor", "regra": "?", "parametro": "-", "mensagem_erro": "O valor da(s) liquidação(ões) de empenho, subtraído a(s) anulação(ões) de empenho não pode ser maior que o valor total do empenho.", "impeditiva": true, "numero_regra": "1187", "exercicio_inicial": "2022"}, {"layout": "AnulacaoRetencaoEmpenho", "campo": "Valor", "regra": "?", "parametro": "-", "mensagem_erro": "O valor excede o valor da Retencao à qual se refere esta anulação.", "impeditiva": true, "numero_regra": "1188", "exercicio_inicial": "2022"}, {"layout": "RetencaoEmpenho", "campo": "NumeroDocumento", "regra": "FOREIGN_KEY_OR", "parametro": "LiquidacaoEmpenho,numero,PagamentoEmpenho,numero", "mensagem_erro": "É preciso que o campo (NumeroDocumento) tenha um registro correspondente no leiaute (LiquidacaoEmpenho) ou (PagamentoEmpenho).", "impeditiva": true, "numero_regra": "1189", "exercicio_inicial": "2024"}, {"layout": "RestosPagar", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (Empenho).", "impeditiva": true, "numero_regra": "1189", "exercicio_inicial": "2022"}, {"layout": "RestosPagar", "campo": "CodigoUnidadeGestora", "numero_regra": "1190", "regra": "FOREIGN_KEY", "parametro": "UnidadeGestora,id_cardug", "mensagem_erro": "É preciso que o campo (CodigoUnidadeGestora) tenha um registro correspondente no Sistema CARDUG.", "impeditiva": true, "exercicio_inicial": "2022"}, {"layout": "RestosPagar", "campo": "CodigoUnidadeOrcamentaria", "numero_regra": "1191", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "RestosPagar", "campo": "CodigoUnidadeOrcamentaria", "numero_regra": "1191", "regra": "FOREIGN_KEY_OR", "parametro": "UnidadeOrcamentaria,codigo,UnidadeOrcamentariaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente nos leiautes (UnidadeOrcamentaria) ou (UnidadeOrcamentariaInicial).", "impeditiva": true, "exercicio_inicial": "2025"}, {"layout": "RestosPagar", "campo": "CodigoPrograma", "numero_regra": "1193", "regra": "FOREIGN_KEY", "parametro": "Programa,codigo", "mensagem_erro": "É preciso que o campo (CodigoPrograma) tenha um registro correspondente no leiaute (Programa).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "RestosPagar", "campo": "CodigoPrograma", "numero_regra": "1193", "regra": "FOREIGN_KEY_OR", "parametro": "Programa,codigo,ProgramaInicial,codigo", "mensagem_erro": "É preciso que o campo (CodigoPrograma) tenha um registro correspondente nos leiautes (Programa) ou (ProgramaInicial).", "impeditiva": false, "exercicio_inicial": "2025"}, {"layout": "RestosPagar", "campo": "NumeroAcao", "numero_regra": "1194", "regra": "FOREIGN_KEY", "parametro": "Acao,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente no leiaute (Acao).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "RestosPagar", "campo": "NumeroAcao", "numero_regra": "1194", "regra": "FOREIGN_KEY_OR", "parametro": "Acao,numero,AcaoInicial,numero", "mensagem_erro": "É preciso que o campo (NumeroAcao) tenha um registro correspondente nos leiautes (Acao) ou (AcaoInicial).", "impeditiva": false, "exercicio_inicial": "2025"}, {"layout": "RestosPagar", "campo": "ContaContabil", "numero_regra": "1195", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "exercicio_inicial": "2022"}, {"layout": "RestosPagar", "campo": "NumeroLicitacao", "numero_regra": "1196", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário ter um registro correspondente no leiaute (Licitacao) ou (ContratacaoDireta).", "impeditiva": false, "exercicio_inicial": "2022"}, {"layout": "RestosPagar", "campo": "NumeroContratacao", "numero_regra": "1196", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário ter um registro correspondente no leiaute (Licitacao) ou (ContratacaoDireta).", "impeditiva": false, "exercicio_inicial": "2022"}, {"layout": "RestosPagar", "campo": "NumeroConvenio", "numero_regra": "1197", "regra": "FOREIGN_KEY", "parametro": "Convenios,numero_convenio", "mensagem_erro": "É preciso que o campo (NumeroConvenio) tenha um registro correspondente no leiaute (Convenio).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "RestosPagar", "campo": "NumeroProcesso", "numero_regra": "1198", "regra": "FOREIGN_KEY_OR", "parametro": "Licitacao,numero_processo,ContratacaoDireta,numero_processo", "mensagem_erro": "É necessário ter um registro correspondente no leiaute (Licitacao) ou (ContratacaoDireta).", "impeditiva": false, "exercicio_inicial": "2022"}, {"layout": "RestosPagar", "campo": "Valor", "numero_regra": "1199", "regra": "?", "parametro": "", "mensagem_erro": "O valor do Restos à Pagar, subtraído a(s) anulação(ões) de empenho, não pode ser maior que o valor total do empenho.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoRestosPagar", "campo": "NumeroRestosPagar", "regra": "FOREIGN_KEY_OR", "parametro": "RestosPagar,numero,RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroRestosPagar) tenha um registro correspondente no leiaute (RestosPagar) nos campos (NumeroRestosPagar) ou (NumeroEmpenho).", "impeditiva": true, "numero_regra": "1200", "exercicio_inicial": "2022"}, {"layout": "AnulacaoRestosPagar", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (RestosPagar).", "impeditiva": true, "numero_regra": "1201", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoRestosPagar", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1202", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoRestosPagar", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1203", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "LiquidacaoRestosPagar", "campo": "NumeroRestosPagar", "regra": "FOREIGN_KEY_OR", "parametro": "RestosPagar,numero,RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroRestosPagar) tenha um registro correspondente no leiaute (RestosPagar) nos campos (NumeroRestosPagar) ou (NumeroEmpenho).", "impeditiva": true, "numero_regra": "1210", "exercicio_inicial": "2022"}, {"layout": "LiquidacaoRestosPagar", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (RestosPagar).", "impeditiva": true, "numero_regra": "1211", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "LiquidacaoRestosPagar", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1212", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "LiquidacaoRestosPagar", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1213", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "LiquidacaoRestosPagar", "campo": "Valor", "regra": "?", "parametro": "Se o valor do campo (Valor) definido no leiaute (LiquidacaoRestosPagar), subtraído o valor do campo (Valor) definido no leiaute (AnulacaoRestosPagar), ou o somatório deste campo no caso de anulações parciais, for maior que o valor do campo (Valor) definido no leiaute (RestosPagar).", "mensagem_erro": "O valor das Liquidações de Restos a Pagar, subtraído a(s) anulação(ões) dos Restos a Pagar, não pode ser maior que o valor total dos Restos a Pagar.", "impeditiva": true, "numero_regra": "1214", "exercicio_inicial": "2022"}, {"layout": "AnulacaoLiquidacaoRestosPagar", "campo": "NumeroLiquidacaoRestosPagar", "regra": "FOREIGN_KEY", "parametro": "LiquidacaoRestosPagar,numero", "mensagem_erro": "É preciso que o campo (NumeroLiquidacaoRestosPagar) tenha um registro correspondente no leiaute (LiquidacaoRestosPagar).", "impeditiva": true, "numero_regra": "1220", "exercicio_inicial": "2022"}, {"layout": "AnulacaoLiquidacaoRestosPagar", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (RestosPagar).", "impeditiva": true, "numero_regra": "1221", "exercicio_inicial": "2022"}, {"layout": "AnulacaoLiquidacaoRestosPagar", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "numero_regra": "1222", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoLiquidacaoRestosPagar", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1223", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoLiquidacaoRestosPagar", "campo": "Valor", "regra": "?", "parametro": "Se o valor do campo (Valor) definido no leiaute (AnulacaoLiquidacaoRestosPagar), subtraído o valor do campo (Valor) definido no leiaute (AnulacaoRestosPagar), ou o somatório deste campo no caso de anulações parciais, for maior que o valor do campo (Valor) definido no leiaute (RestosPagar).", "mensagem_erro": "O valor das Liquidações de Restos a Pagar, subtraído a(s) anulação(ões) dos Restos a Pagar, não pode ser maior que o valor total dos Restos a Pagar.", "impeditiva": true, "numero_regra": "1224", "exercicio_inicial": "2022"}, {"layout": "PagamentoRestosPagar", "campo": "NumeroRestosPagar", "regra": "FOREIGN_KEY_OR", "parametro": "RestosPagar,numero,RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroRestosPagar) tenha um registro correspondente no leiaute (RestosPagar) nos campos (NumeroRestosPagar) ou (NumeroEmpenho).", "impeditiva": true, "numero_regra": "1230", "exercicio_inicial": "2022"}, {"layout": "PagamentoRestosPagar", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (RestosPagar).", "impeditiva": true, "numero_regra": "1231", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "PagamentoRestosPagar", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria) do leiaute de Orçamento.", "impeditiva": true, "numero_regra": "1232", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "PagamentoRestosPagar", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1233", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "PagamentoRestosPagar", "campo": "Valor", "regra": "?", "parametro": "Se o valor do campo (Valor) definido no leiaute (PagametoRestosPagar), for maior que o valor do campo (Valor) definido no leiaute (LiquidacaoRestosPagar).", "mensagem_erro": "O valor do Pagamentos dos Restos a Pagar, não pode ser maior que o valor total das Liquidações do Restos a Pagar.", "impeditiva": true, "numero_regra": "1234", "exercicio_inicial": "2022"}, {"layout": "AnulacaoPagamentoRestosPagar", "campo": "NumeroPagamentoRestosPagar", "regra": "FOREIGN_KEY", "parametro": "PagamentoRestosPagar,numero", "mensagem_erro": "É preciso que o campo (NumeroPagamentoRestosPagar) tenha um registro correspondente no leiaute (PagamentoRestosPagar).", "impeditiva": true, "numero_regra": "1240", "exercicio_inicial": "2022"}, {"layout": "AnulacaoPagamentoRestosPagar", "campo": "NumeroEmpenho", "regra": "FOREIGN_KEY", "parametro": "RestosPagar,numero_empenho ", "mensagem_erro": "É preciso que o campo (NumeroEmpenho) tenha um registro correspondente no leiaute (RestosPagar) nos campos (NumeroEmpenho).", "impeditiva": true, "numero_regra": "1241", "exercicio_inicial": "2022"}, {"layout": "AnulacaoPagamentoRestosPagar", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria) do leiaute de Orçamento.", "impeditiva": true, "numero_regra": "1242", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoPagamentoRestosPagar", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1243", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoPagamentoRestosPagar", "campo": "Valor", "regra": "?", "parametro": "Se o valor do campo (Valor) definido no leiaute (AnulacaoPagametoRestosPagar), for maior que o valor do campo (Valor) definido no leiaute (PagamentoRestosPagar).", "mensagem_erro": "O valor da Anulação do Pagamento dos Restos a Pagar, não pode ser maior que o valor total do Pagamento do Restos a Pagar.", "impeditiva": true, "numero_regra": "1244", "exercicio_inicial": "2022"}, {"layout": "RetencaoRestosPagar", "campo": "NumeroRestosPagar", "regra": "FOREIGN_KEY_OR", "parametro": "RestosPagar,numero,RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroRestosPagar) tenha um registro correspondente no leiaute (RestosPagar) nos campos (NumeroRestosPagar) ou (NumeroEmpenho).", "impeditiva": true, "numero_regra": "1250", "exercicio_inicial": "2022"}, {"layout": "RetencaoRestosPagar", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria) do leiaute de Orçamento.", "impeditiva": true, "numero_regra": "1251", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "RetencaoRestosPagar", "campo": "NumeroLiquidacaoRestosPagar", "numero_regra": "1252", "regra": "FOREIGN_KEY_OR", "parametro": "LiquidacaoRestosPagar,numero,LiquidacaoEmpenho,numero", "mensagem_erro": "É preciso que o campo (NumeroLiquidacaoRestosPagar) tenha um registro correspondente no leiaute (LiquidacaoRestosPagar) ou no leiaute (LiquidacaoEmpenho).", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "RetencaoRestosPagar", "campo": "NumeroPagamentoRestosPagar", "numero_regra": "1253", "regra": "FOREIGN_KEY", "parametro": "PagamentoRestosPagar,numero", "mensagem_erro": "É preciso que o campo (NumeroPagamentoRestosPagar) tenha um registro correspondente no leiaute (PagamentoRestosPagar)", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "AnulacaoRetencaoRestosPagar", "campo": "NumeroRestosPagar", "regra": "FOREIGN_KEY_OR", "parametro": "RestosPagar,numero,RestosPagar,numero_empenho", "mensagem_erro": "É preciso que o campo (NumeroRestosPagar) tenha um registro correspondente no leiaute (RestosPagar) nos campos (NumeroRestosPagar) ou (NumeroEmpenho).", "impeditiva": true, "numero_regra": "1255", "exercicio_inicial": "2022"}, {"layout": "AnulacaoRetencaoRestosPagar", "campo": "CodigoUnidadeOrcamentaria", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria) do leiaute de Orçamento.", "impeditiva": true, "numero_regra": "1256", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AnulacaoRetencaoRestosPagar", "campo": "NumeroRetencaoRestosPagar", "regra": "FOREIGN_KEY", "parametro": "RetencaoRestosPagar,numero", "mensagem_erro": "É preciso que o campo (NumeroRetencaoRestosPagar) tenha um registro correspondente no leiaute (RetencaoRestosPagar).", "impeditiva": true, "numero_regra": "1257", "exercicio_inicial": "2024"}, {"layout": "AnulacaoRetencaoRestosPagar", "campo": "NumeroLiquidacaoRestosPagar", "regra": "FOREIGN_KEY_OR", "parametro": "LiquidacaoRestosPagar,numero,PagamentoRestosPagar,numero", "mensagem_erro": "É preciso que o campo (NumeroLiquidacaoRestosPagar) tenha um registro correspondente no leiaute (LiquidacaoRestosPagar) no campo (Numero) ou no leiaute PagamentoRestosPagar no campo (Numero).", "impeditiva": true, "numero_regra": "1258", "exercicio_inicial": "2024"}, {"layout": "LocalizacaoImovel", "campo": "CodigoBem", "regra": "FOREIGN_KEY", "parametro": "BemPatrimonial,codigo", "mensagem_erro": "É preciso que o campo (CodigoBem) tenha um registro correspondente no leiaute (BemPatrimonial).", "impeditiva": true, "numero_regra": "1280", "exercicio_inicial": "2022"}, {"layout": "MovimentacaoBem", "campo": "CodigoBem", "regra": "FOREIGN_KEY", "parametro": "BemPatrimonial,codigo", "mensagem_erro": "É preciso que o campo (CodigoBem) tenha um registro correspondente no leiaute (BemPatrimonial).", "impeditiva": true, "numero_regra": "1290", "exercicio_inicial": "2022"}, {"layout": "MovimentacaoBem", "campo": "ContaContabil", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,conta", "mensagem_erro": "É preciso que o campo (ContaContabil) tenha um registro correspondente no leiaute (MatrizSaldosContabeis).", "impeditiva": true, "numero_regra": "1292", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "Licitacao", "campo": "JustificativaGrupoLote", "regra": "CONDITIONAL_MANDATORY", "parametro": "Licitacao,Agrupamento,2", "mensagem_erro": "O campo (JustificativaGrupoLote) é obrigatório pois (Agrupamento) recebeu valor 2 (Grupo/Lote)", "impeditiva": true, "numero_regra": "2000", "exercicio_inicial": "2022"}, {"layout": "Licitacao", "campo": "RegistroPreco", "regra": "CONDITIONAL_BY_VALUE", "parametro": "1,<PERSON><PERSON><PERSON><PERSON>,4|5|6|7,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,3|4", "mensagem_erro": "A (Modalidade) e (NaturezaObjeto) informados são incompatíveis com (RegistroPreco), as Modalidades permitidas são do tipo 4. <PERSON><PERSON> de Preços, 5. Con<PERSON>rência, 6. Pregão Presencial e 7. Pregão Eletronico e a Natureza do Objeto deve ser 3. Compras ou 4. Serviços - exceto engenharia.", "impeditiva": true, "numero_regra": "2001", "exercicio_inicial": "2022"}, {"layout": "Licitacao", "campo": "PossuiParticipantes", "regra": "?", "parametro": "Se o campo (PossuiParticipantes) receber SIM então (RegistroPreco) deve ser igual à SIM.", "mensagem_erro": "A Licitação possui participantes, portanto deve ser um Registro de Preço (o campo RegistroPreco deve receber o valor 1. SIM)..", "impeditiva": true, "numero_regra": "2002", "exercicio_inicial": "2022"}, {"layout": "Licitacao", "campo": "RegimeExecucaoObra", "regra": "CONDITIONAL_MANDATORY", "parametro": "Licitacao,NaturezaObjeto,1|2", "mensagem_erro": "O campo (RegimeExecucaoObra) é obrigatório quando (NaturezaObjeto) receber valor igual à 1, <PERSON><PERSON><PERSON> ou 2, Serviços de Engenharia.", "impeditiva": true, "numero_regra": "2003", "exercicio_inicial": "2022"}, {"layout": "Licitacao", "campo": "NaturezaObra", "regra": "CONDITIONAL_MANDATORY", "parametro": "Licitacao,NaturezaObjeto,1|2", "mensagem_erro": "O campo (NaturezaObra) é obrigatório quando (NaturezaObjeto) receber valor igual à 1, <PERSON><PERSON><PERSON> ou 2, Serviços de Engenharia.", "impeditiva": true, "numero_regra": "2004", "exercicio_inicial": "2022"}, {"layout": "AgenteContratacao", "campo": "Modalidade", "regra": "?", "parametro": "Se caso o campo (Modalidade), definido no le<PERSON>ute (Licitacao) receber valor 6-Pregão Presencial ou 7-Pregão Eletrônico o campo (PapelAgente) deve receber o valor 1-<PERSON><PERSON><PERSON>.", "mensagem_erro": "A modalidade da Licitação cujo servidor foi designado como Agente de Contratação é do Tipo Pregão Presencial ou Eletrônico e por isso é preciso informar um Pregoeiro.", "impeditiva": true, "numero_regra": "2012", "exercicio_inicial": "2022"}, {"layout": "ItemLicitacao", "campo": "NumeroLicitacao", "parametro": "NumeroContratacao", "regra": "REQUIRED_IF", "mensagem_erro": "O campo NumeroLicitacao é obrigatório quando o campo NumeroContratacao não existe.", "impeditiva": true, "numero_regra": "2020", "exercicio_inicial": "2022"}, {"layout": "ItemLicitacao", "campo": "ValorUnitario<PERSON>o", "regra": "CONDITIONAL_MANDATORY", "parametro": "<PERSON>citacao,RegistroPreco,1", "mensagem_erro": "O campo (ValorUnitarioEstimado) é obrigatório quando se tratar de um Registro de Preço (RegistroPreco).", "impeditiva": true, "numero_regra": "2021", "exercicio_inicial": "2022"}, {"layout": "ItemLicitacao", "campo": "NumeroContratacao", "numero_regra": "2022", "parametro": "NumeroLicitacao", "regra": "REQUIRED_IF", "mensagem_erro": "O campo NumeroContratacao é obrigatório quando o campo NumeroLicitacao não existe.", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "OrgaoParticipanteItem", "campo": "CNPJOrgaoParticipante", "regra": "FOREIGN_KEY", "parametro": "OrgaoParticipante,cnpj", "mensagem_erro": "Não foi encontrada correspondência do órgão participante (CNPJOrgaoParticipante). É necessário informar os dados do Órgão que participa.", "impeditiva": true, "numero_regra": "2046", "exercicio_inicial": "2022"}, {"layout": "AdesaoRegistroDePrecos", "campo": "FormaPregao", "regra": "CONDITIONAL_MANDATORY", "parametro": "AdesaoRegistroDePrecos,Modalidade,2", "mensagem_erro": "<PERSON> campo (FormaPregao) é obrigatório pois (Modalidade) recebeu valor 2, Preg<PERSON>.", "impeditiva": true, "numero_regra": "2051", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataAdjudicacao", "regra": "?", "parametro": "Licitacao,data_publicacao_edital", "mensagem_erro": "A data informada em (DataAdjudicacao) não pode ser anterior à data de publicação do edital (DataPublicacaoEdital).", "impeditiva": true, "numero_regra": "2061", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataHomologacao", "regra": "?", "parametro": "Licitacao,data_publicacao_edital", "mensagem_erro": "A data informada em (DataHomologacao) não pode ser anterior à data de publicação do edital (DataPublicacaoEdital).", "impeditiva": true, "numero_regra": "2062", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataPublicacaoResultado", "regra": "?", "parametro": "Licitacao,data_publicacao_edital", "mensagem_erro": "A data informada em (DataPublicacaoResultado) não pode ser anterior à data de publicação do edital (DataPublicacaoEdital).", "impeditiva": true, "numero_regra": "2063", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataAnulacao", "regra": "?", "parametro": "Se o campo (DataAnulacao) não for vazio, então o campo (MotivoAnulacao) torna-se obrigatório.", "mensagem_erro": "Foi informada uma data de anulação (DataAnulacao) portanto a justificativa (MotivoAnulacao) é obrigatória.", "impeditiva": true, "numero_regra": "2065", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataPublicacaoAnulacao", "regra": "DATE_BEFORE", "parametro": "DataAnulacao", "mensagem_erro": "A data informada em (DataPublicacaoAnulacao) não pode ser anterior à data de anulação (DataAnulacao).", "impeditiva": true, "numero_regra": "2066", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataRevogacao", "regra": "?", "parametro": "Licitacao,data_publicacao_edital", "mensagem_erro": "A data informada em (DataRevogacao) não pode ser anterior à data de publicação do edital (DataPublicacaoEdital).", "impeditiva": true, "numero_regra": "2068", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataPublicacaoRevogacao", "regra": "DATE_BEFORE", "parametro": "DataRevogacao", "mensagem_erro": "A data informada em (DataPublicacaoRevogacao) não pode ser anterior à data de revogação (DataRevogacao).", "impeditiva": true, "numero_regra": "2069", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataRevogacao", "regra": "?", "parametro": "Se o campo (DataRevogacao) não for vazio, então o campo (MotivoRevogacao) torna-se obrigatório.", "mensagem_erro": "Foi informada uma data de revogação (DataRevogacao) portanto a justificativa (MotivoRevogacao) é obrigatória.", "impeditiva": true, "numero_regra": "2071", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "DataVigencia", "regra": "CONDITIONAL_BY_LAYOUT", "parametro": "Licitacao,NumeroLicitacao,RegistroPreco,1", "mensagem_erro": "A licitação é do tipo Registro de Preço, portanto a data de vigência da adjudicação (DataVigenciaAdjudicacao) é obrigatória.", "impeditiva": true, "numero_regra": "2072", "exercicio_inicial": "2022"}, {"layout": "ProponenteLicitacaoItem", "campo": "CodigoProponente", "regra": "FOREIGN_KEY", "parametro": "ProponenteLicitacao,codigo", "mensagem_erro": "Não foi encontrada correspondência do Item (CodigoProponente). É necessário informar os dados do proponente no leiaute (ProponenteLicitacao).", "impeditiva": true, "numero_regra": "2084", "exercicio_inicial": "2024"}, {"layout": "ProponenteLicitacaoItem", "campo": "CodigoItem", "regra": "FOREIGN_KEY", "parametro": "ItemLicitacao,codigo", "mensagem_erro": "Não foi encontrada correspondência do Item (CodigoItem). É necessário informar os dados do item no leiaute (ItemLicitacao).", "impeditiva": true, "numero_regra": "2086", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "ProponenteLicitacaoItem", "campo": "CodigoParticipante", "regra": "FOREIGN_KEY", "parametro": "Proponente<PERSON>icit<PERSON>o,codigo_participante", "mensagem_erro": "Não foi encontrada correspondência do Item (CodigoParticipante). É necessário informar os dados do proponente no leiaute (ProponenteLicitacao).", "impeditiva": true, "numero_regra": "2087", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "ItemAdjudicado", "campo": "VencedorItem", "regra": "FOREIGN_KEY", "parametro": "ProponenteLicitacao,codigo", "mensagem_erro": "O campo (VencedorItem) não tem um registro correspondente no leiaute (ProponenteLicitacao).", "impeditiva": true, "numero_regra": "2092", "exercicio_inicial": "2022"}, {"layout": "GrupoAdjudicado", "campo": "NumeroLote", "numero_regra": "2101", "regra": "FOREIGN_KEY", "parametro": "GrupoLicitacao,numero_grupo", "mensagem_erro": "O campo (NumeroLote) não tem um registro correspondente no leiaute (GrupoLicitacao).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "GrupoAdjudicado", "campo": "NumeroGrupo", "numero_regra": "2103", "regra": "FOREIGN_KEY", "parametro": "GrupoLicitacao,numero_grupo", "mensagem_erro": "O campo (NumeroGrupo) não tem um registro correspondente no leiaute (GrupoLicitacao).", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "AtaRegistroDePreco", "campo": "InicioVigencia", "regra": "?", "parametro": "Se a data informada no campo (InicioVigencia) for anterior à data informada no campo (DataHomologacao), declarado no leiaute (AdjudicacaoLicitacao).", "mensagem_erro": "A data informada em (InicioVigencia) não pode ser anterior à data de homologação (DataHomologacao), declarado no leiaute (AdjudicacaoLicitacao).", "impeditiva": true, "numero_regra": "2111", "exercicio_inicial": "2022"}, {"layout": "AtaRegistroDePreco", "campo": "FimVigencia", "regra": "DATE_BEFORE", "parametro": "InicioVigencia", "mensagem_erro": "A data final informada em (FimVigencia) não pode ser anterior à data de início (InicioVigencia).", "impeditiva": true, "numero_regra": "2112", "exercicio_inicial": "2022"}, {"layout": "AtaRegistroDePreco", "campo": "DataPublicacaoAta", "regra": "DATE_BEFORE", "parametro": "InicioVigencia", "mensagem_erro": "A data informada em (DataPublicacaoAta) não pode ser anterior à data de início (InicioVigencia).", "impeditiva": true, "numero_regra": "2113", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "Valor", "regra": "?", "parametro": "Se o valor do campo (Valor) for maior do valor declarado no campo (ValorMaximo), no leiaute (Licitacao).", "mensagem_erro": "O valor informado (Valor) é maior que o valor máximo (ValorMaximo) informado na tabela (Licitacao)", "impeditiva": true, "numero_regra": "2121", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "BaseLegalPagamentoAntecipado", "regra": "CONDITIONAL_MANDATORY", "parametro": "<PERSON><PERSON><PERSON>,PagamentoAntecipado,1", "mensagem_erro": "O campo (BaseLegalPagamentoAntecipado) é o obrigatório pois o campo (PagamentoAntecipado) recebeu valor 1, SIM.", "impeditiva": true, "numero_regra": "2122", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "InicioVigencia", "regra": "?", "parametro": "Se a data informada no campo (InicioVigencia) for anterior à data informada no campo (DataPublicacaoAta), declarado no leiaute (AtaRegistroDePreco).", "mensagem_erro": "A data informada como início da vigência (InicioVigencia) do contrato, é anterior à data de publicação da ata de registro de preço (DataPublicacaoAta), declarado no leiaute (AtaRegistroDePreco).", "impeditiva": true, "numero_regra": "2124", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "FimVigencia", "regra": "DATE_BEFORE", "parametro": "InicioVigencia", "mensagem_erro": "A data informada como (FimVigencia) do contrato, é anterior à data informada como (InicioVigencia).", "impeditiva": true, "numero_regra": "2125", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "Tipo", "regra": "?", "parametro": "O valor do campo (Tipo) deve existir na Tabela 07 – Tipos de Contrato", "mensagem_erro": "O valor informado no tipo de contrato (Tipo) não tem correspondência na Tabela 07 – Tipos de Contrato", "impeditiva": true, "numero_regra": "2126", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "ReferenciaLegalDispensa", "regra": "?", "parametro": "O valor do campo (ReferenciaLegalDispensa) deve existir na Tabela 08 – Referencia Legal para Dispensa de Licitação ou Inexigibilidade", "mensagem_erro": "O valor informado no tipo de contrato (ReferenciaLegalDispensa) não tem correspondência na Tabela 08 – Referencia Legal para Dispensa de Licitação ou Inexigibilidade", "impeditiva": true, "numero_regra": "2127", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "NumeroAtaRegistroPreco", "regra": "CONDITIONAL_MANDATORY", "parametro": "<PERSON><PERSON><PERSON>,TipoProcessoContratacao,3|4|5", "mensagem_erro": "O campo (NumeroAtaRegistroPreco) é obrigatório, pois o campo (TipoProcessoContratacao) recebeu valor, Ata de Registro de Preço, 4, Participação de Ata de Registro de Preço ou 5, Adesão à ata de regitro de preço.", "impeditiva": true, "numero_regra": "2128", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "InicioVigenciaGarantia", "regra": "DATE_BEFORE", "parametro": "InicioVigencia", "mensagem_erro": "A data do início da vigência da garantia do contrato (InicioVigenciaGarantia) é anterior à data de início do contrato (InicioVigencia).", "impeditiva": true, "numero_regra": "2130", "exercicio_inicial": "2022"}, {"layout": "Contrato", "campo": "FimVigenciaGarantia", "regra": "DATE_BEFORE", "parametro": "InicioVigenciaGarantia", "mensagem_erro": "A data de fim da vigência da garantia (FimVigenciaGarantia) é anterior à data de início da garantia (InicioVigenciaGarantia).", "impeditiva": true, "numero_regra": "2131", "exercicio_inicial": "2022"}, {"layout": "AlteracaoAtaRegistroDePreco", "campo": "JustificativaOutroTipoAditivo", "regra": "CONDITIONAL_MANDATORY", "parametro": "AlteracaoAtaRegistroDePreco,TipoAditivo,4", "mensagem_erro": "O campo (JustificativaOutroTipoAditivo) é obrigatório, pois o campo (TipoAditivo) recebeu valor, 4, <PERSON><PERSON>.", "impeditiva": true, "numero_regra": "2141", "exercicio_inicial": "2022"}, {"layout": "AlteracaoAtaRegistroDePreco", "campo": "FimVigencia", "regra": "?", "parametro": "Se a data informada no campo (FimVigencia) for anterior à data informada no campo (FimVigencia), declarado no leiaute (Contrato).", "mensagem_erro": "A data informada como (FimVigencia) do contrato, é anterior à data informada como (FimVigencia), declarado no leiaute (Contrato).", "impeditiva": true, "numero_regra": "2142", "exercicio_inicial": "2022"}, {"layout": "AlteracaoAtaRegistroDePreco", "campo": "FimVigencia", "regra": "CONDITIONAL_MANDATORY", "parametro": "AlteracaoAtaRegistroDePreco,TipoAditivo,1|3", "mensagem_erro": "O campo (FimVigencia) é obrigatório, pois o campo (TipoAditivo) recebeu valor, 1, Aditivo de prazo ou 3, Aditivo de prazo e valor unitário.", "impeditiva": true, "numero_regra": "2143", "exercicio_inicial": "2022"}, {"layout": "AditivoContrato", "campo": "Vigência", "regra": "CONDITIONAL_MANDATORY", "parametro": "AditivoContrato,TipoAditivo,2|4|5", "mensagem_erro": "O campo (Vigência) é obrigatório, pois o campo (TipoAditivo) recebeu valor, 2, Aditivo de prazo, 4, Aditivo de prazo e valor unitário ou 5, Aditivo de prazo e quantidade", "impeditiva": true, "numero_regra": "2151", "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AditivoContrato", "campo": "Vigência", "regra": "?", "parametro": "Se a data informada no campo (Vigência) for anterior à data informada no campo (FimVigencia), declarado no leiaute (Contrato).", "mensagem_erro": "A data informada como (Vigência) do contrato, é anterior à data informada como (FimVigencia), declarado no leiaute (Contrato).", "impeditiva": true, "numero_regra": "2152", "exercicio_inicial": "2022"}, {"layout": "AditivoContrato", "campo": "JustificativaOutroTipoAditivo", "regra": "CONDITIONAL_MANDATORY", "parametro": "AditivoContrato,TipoAditivo,7", "mensagem_erro": "O campo (JustificativaOutroTipoAditivo) é obrigatório, pois o campo (TipoAditivo) recebeu valor, 7, <PERSON><PERSON>.", "impeditiva": true, "numero_regra": "2153", "exercicio_inicial": "2022"}, {"layout": "MetasExecucaoContrato", "campo": "DataFim", "regra": "?", "parametro": "Se a data informada no campo (DataFim) for anterior à data informada no campo (DataInicio).", "mensagem_erro": "A data informada como (DataFim) do contrato, é anterior à data informada como (DataInicio).", "impeditiva": true, "numero_regra": "2167", "exercicio_inicial": "2022"}, {"layout": "EtapaCronogramaFisico", "campo": "DataFim", "regra": "DATE_BEFORE", "parametro": "DataInicio", "mensagem_erro": "A data informada como (DataFim) do contrato, é anterior à data informada como (DataInicio).", "impeditiva": true, "numero_regra": "2172", "exercicio_inicial": "2022"}, {"layout": "AtaRegistroDePreco", "campo": "InicioVigencia", "numero_regra": "2111", "regra": "?", "parametro": "AdjudicacaoLicitacao,data_homologacao", "mensagem_erro": "A data informada em (InicioVigencia) não pode ser anterior à data de homologação (DataHomologacao), declarado no leiaute (AdjudicacaoLicitacao).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AtaRegistroDePreco", "campo": "FimVigencia", "numero_regra": "2112", "regra": "?", "parametro": "InicioVigencia", "mensagem_erro": "A data final informada em (FimVigencia) não pode ser anterior à data de início (InicioVigencia).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AtaRegistroDePreco", "campo": "DataPublicacaoAta", "numero_regra": "2113", "regra": "?", "parametro": "InicioVigencia", "mensagem_erro": "A data informada em (DataPublicacaoAta) não pode ser anterior à data de início (InicioVigencia).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AtaRegistroDePreco", "campo": "VeiculoPublicacaoAta", "numero_regra": "2114", "regra": "?", "parametro": "", "mensagem_erro": "É preciso que o campo (VeiculoPublicacaoAta) tenha um registro correspondente na Tabela 01 – Veículos de Publicação.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "NumeroLicitacao", "numero_regra": "2120", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "O campo (NumeroLicitacao) não tem um registro correspondente no leiaute (Licitacao).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "Valor", "numero_regra": "2121", "regra": "?", "parametro": "Licita<PERSON><PERSON>,valor_maximo", "mensagem_erro": "O valor informado (Valor) é maior que o valor máximo (ValorMaximo) informado na tabela (Licitacao)", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "PagamentoAntecipado", "numero_regra": "2122", "regra": "?", "parametro": "1, BaseLegalPagamentoAntecipado", "mensagem_erro": "O campo (BaseLegalPagamentoAntecipado) é o obrigatório pois o campo (PagamentoAntecipado) recebeu valor 1, SIM.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "InicioVigencia", "numero_regra": "2124", "regra": "?", "parametro": "AtaRegistroDePreco,data_publicacao_ata", "mensagem_erro": "A data informada como início da vigência (InicioVigencia) do contrato, é anterior à data de publicação da ata de registro de preço (DataPublicacaoAta), declarado no leiaute (AtaRegistroDePreco).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "FimVigencia", "numero_regra": "2125", "regra": "?", "parametro": "InicioVigencia", "mensagem_erro": "A data informada como (FimVigencia) do contrato, é anterior à data informada como (InicioVigencia).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "Tipo", "numero_regra": "2126", "regra": "?", "parametro": "", "mensagem_erro": "O valor informado no tipo de contrato (Tipo) não tem correspondência na Tabela 07 – Tipos de Contrato", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "ReferenciaLegalDispensa", "numero_regra": "2127", "regra": "?", "parametro": "", "mensagem_erro": "O valor informado no tipo de contrato (ReferenciaLegalDispensa) não tem correspondência na Tabela 08 – Referencia Legal para Dispensa de Licitação ou Inexigibilidade", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "TipoProcessoContratacao", "numero_regra": "2128", "regra": "?", "parametro": "3, Ata de Registro de Preço; 4, Participação de Ata de Registro de Preço; 5, Adesão à ata de regitro de preço", "mensagem_erro": "O campo (NumeroAtaRegistroPreco) é obrigatório, pois o campo (TipoProcessoContratacao) recebeu valor, Ata de Registro de Preço, 4, Participação de Ata de Registro de Preço ou 5, Adesão à ata de regitro de preço.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "InicioVigenciaGarantia", "numero_regra": "2130", "regra": "?", "parametro": "<PERSON><PERSON><PERSON>,inicio_vigencia", "mensagem_erro": "A data do início da vigência da garantia do contrato (InicioVigenciaGarantia) é anterior à data de início do contrato (InicioVigencia).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "FimVigenciaGarantia", "numero_regra": "2131", "regra": "?", "parametro": "InicioVigenciaGarantia", "mensagem_erro": "A data de fim da vigência da garantia (FimVigenciaGarantia) é anterior à data de início da garantia (InicioVigenciaGarantia).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Contrato", "campo": "NumeroContratacao", "numero_regra": "2132", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "O campo (NumeroContratacao) não tem um registro correspondente no leiaute (ContratacaoDireta).", "impeditiva": false, "exercicio_inicial": "2024", "exercicio_final": null}, {"layout": "AlteracaoAtaRegistroDePreco", "campo": "NumeroLicitacao", "numero_regra": "2140", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "O campo (NumeroLicitacao) não tem um registro correspondente no leiaute (Licitacao).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AlteracaoAtaRegistroDePreco", "campo": "TipoAditivo", "numero_regra": "2141", "regra": "?", "parametro": "4, <PERSON><PERSON>", "mensagem_erro": "O campo (JustificativaOutroTipoAditivo) é obrigatório, pois o campo (TipoAditivo) recebeu valor, 4, <PERSON><PERSON>.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AlteracaoAtaRegistroDePreco", "campo": "FimVigencia", "numero_regra": "2142", "regra": "?", "parametro": "Contrato,fim_vigencia", "mensagem_erro": "A data informada como (FimVigencia) do contrato, é anterior à data informada como (FimVigencia), declarado no leiaute (Contrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AlteracaoAtaRegistroDePreco", "campo": "TipoAditivo", "numero_regra": "2143", "regra": "?", "parametro": "1, Aditivo de prazo; 3, Aditivo de prazo e valor unitário", "mensagem_erro": "O campo (FimVigencia) é obrigatório, pois o campo (TipoAditivo) recebeu valor, 1, Aditivo de prazo ou 3, Aditivo de prazo e valor unitário.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AlteracaoAtaRegistroDePreco", "campo": "VeiculoPublicacaoAta", "numero_regra": "2145", "regra": "?", "parametro": "", "mensagem_erro": "É preciso que o campo (VeiculoPublicacaoAta) tenha um registro correspondente na Tabela 01 – Veículos de Publicação.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AditivoContrato", "campo": "NumeroContrato", "numero_regra": "2150", "regra": "FOREIGN_KEY", "parametro": "Contrato,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente no leiaute (Contrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AditivoContrato", "campo": "TipoAditivo", "numero_regra": "2151", "regra": "?", "parametro": "2, Aditivo de prazo; 4, Aditivo de prazo e valor unitário; 5, Aditivo de prazo e quantidade", "mensagem_erro": "O campo (Vigência) é obrigatório, pois o campo (TipoAditivo) recebeu valor, 2, Aditivo de prazo; 4, Aditivo de prazo e valor unitário; ou 5, Aditivo de prazo e quantidade.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AditivoContrato", "campo": "Vigência", "numero_regra": "2152", "regra": "?", "parametro": "Contrato,fim_vigencia", "mensagem_erro": "A data informada como (Vigência) do contrato, é anterior à data informada como (FimVigencia), declarado no leiaute (Contrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": "2023"}, {"layout": "AditivoContrato", "campo": "TipoAditivo", "numero_regra": "2153", "regra": "?", "parametro": "7, JustificativaOutroTipoAditivo", "mensagem_erro": "O campo (JustificativaOutroTipoAditivo) é obrigatório, pois o campo (TipoAditivo) recebeu valor, 7, <PERSON><PERSON>.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "ItemAditivo", "campo": "NumeroContrato", "numero_regra": "2160", "regra": "FOREIGN_KEY", "parametro": "Contrato,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente no leiaute (Contrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "ItemAditivo", "campo": "NumeroAditivo", "numero_regra": "2161", "regra": "FOREIGN_KEY", "parametro": "AditivoContrato,numero_aditivo", "mensagem_erro": "O campo (NumeroAditivo) não tem um registro correspondente no leiaute (AditivoContrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "ItemAditivo", "campo": "NumeroItem", "numero_regra": "2162", "regra": "FOREIGN_KEY", "parametro": "ItemAdjudicado,numero_item", "mensagem_erro": "O campo (NumeroItem) não tem um registro correspondente no leiaute (ItemAdjudicado).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "ItemAditivo", "campo": "NumeroGrupo", "numero_regra": "2163", "regra": "FOREIGN_KEY", "parametro": "GrupoAdjudicado,numero_grupo", "mensagem_erro": "O campo (NumeroGrupo) não tem um registro correspondente no leiaute (GrupoAdjudicado).", "impeditiva": true, "exercicio_inicial": "2024", "exercicio_final": null}, {"layout": "MetasExecucaoContrato", "campo": "NumeroContrato", "numero_regra": "2165", "regra": "FOREIGN_KEY", "parametro": "Contrato,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente no leiaute (Contrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "MetasExecucaoContrato", "campo": "CodigoPrograma", "numero_regra": "2166", "regra": "FOREIGN_KEY", "parametro": "Programa,codigo", "mensagem_erro": "<PERSON> campo (CodigoPrograma) não tem um registro correspondente no leiaute (Programa).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "MetasExecucaoContrato", "campo": "DataFim", "numero_regra": "2167", "regra": "?", "parametro": "DataInicio", "mensagem_erro": "A data informada como (DataFim) do contrato, é anterior à data informada como (DataInicio).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "MetasExecucaoContrato", "campo": "NumeroLicitacao", "numero_regra": "2168", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "O campo (NumeroLicitacao) não tem um registro correspondente no leiaute (Licitacao).", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "MetasExecucaoContrato", "campo": "NumeroContratacao", "numero_regra": "2169", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "O campo (NumeroContratacao) não tem um registro correspondente no leiaute (ContratacaoDireta).", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "EtapaCronogramaFisico", "campo": "NumeroContrato", "numero_regra": "2170", "regra": "FOREIGN_KEY", "parametro": "Contrato,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente no leiaute (Contrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "EtapaCronogramaFisico", "campo": "NumeroMeta", "numero_regra": "2171", "regra": "FOREIGN_KEY", "parametro": "MetasExecucaoContrato,numero_meta", "mensagem_erro": "O campo (NumeroMeta) não tem um registro correspondente no leiaute (MetasExecucaoContrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "EtapaCronogramaFisico", "campo": "DataFim", "numero_regra": "2172", "regra": "?", "parametro": "DataInicio", "mensagem_erro": "A data informada como (DataFim) do contrato, é anterior à data informada como (DataInicio).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "CronogramaDesembolso", "campo": "NumeroContrato", "numero_regra": "2175", "regra": "FOREIGN_KEY", "parametro": "Contrato,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente na tabela (Contrato).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "CronogramaDesembolso", "campo": "NumeroEtapa", "numero_regra": "2176", "regra": "FOREIGN_KEY", "parametro": "EtapaCronogramaFisico,numero_etapa", "mensagem_erro": "O campo (NumeroEtapa) não tem um registro correspondente na tabela (EtapaCronogramaFisico).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Obra", "campo": "NumeroLicitacao", "numero_regra": "2180", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "O campo (NumeroLicitacao) não tem um registro correspondente na tabela (Licitacao).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Obra", "campo": "NumeroContratacaoDireta", "numero_regra": "2181", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "O campo (NumeroContratacaoDireta) não tem um registro correspondente na tabela (ContratacaoDireta).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Obra", "campo": "NumeroContrato", "numero_regra": "2182", "regra": "FOREIGN_KEY", "parametro": "Contrato,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente na tabela (Contrato).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Obra", "campo": "NaturezaObra", "numero_regra": "2183", "regra": "?", "parametro": "1|2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>zaObjeto", "mensagem_erro": "<PERSON> campo (NaturezaObra) é obrigatório, pois o campo (NaturezaObjeto), dec<PERSON><PERSON> na tabela (Licitacao), rece<PERSON>u valor, 1, <PERSON><PERSON><PERSON> ou 2, Ser<PERSON>ços de engenharia.", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Obra", "campo": "DataFimPrevista", "numero_regra": "2184", "regra": "DATE_BEFORE", "parametro": "DataInicioPrevista", "mensagem_erro": "A data informada como (DataFimPrevista) do contrato, é anterior à data informada como (DataInicioPrevista).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "OrdemServic<PERSON>", "campo": "NumeroContrato", "numero_regra": "2190", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "CadastroNacionalObras", "campo": "NumeroContrato", "numero_regra": "2195", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Acompanhamento", "campo": "NumeroContrato", "numero_regra": "2200", "regra": "FOREIGN_KEY", "parametro": "Contrato,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente na tabela (Contrato).", "impeditiva": false, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "Acompanhamento", "campo": "NumeroParcela", "regra": "FOREIGN_KEY", "parametro": "CronogramaDesembolso,numero_parcela", "mensagem_erro": "O campo (NumeroParcela) não tem um registro correspondente na tabela (CronogramaDesembolso).", "impeditiva": false, "exercicio_inicial": "2022", "numero_regra": "2201"}, {"layout": "Medicao", "campo": "NumeroContrato", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2210"}, {"layout": "Medicao", "campo": "NumeroCNO", "regra": "FOREIGN_KEY", "parametro": "CadastroNacionalObras,numero_cno", "mensagem_erro": "O campo (NumeroCNO) não tem um registro correspondente na tabela (CadastroNacionalObras).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2211"}, {"layout": "DocumentoResponsabilidadeTecnica", "campo": "NumeroContrato", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente na tabela (Obra).", "impeditiva": false, "exercicio_inicial": "2022", "numero_regra": "2215"}, {"layout": "AutorizacaoLicencaAmbiental", "campo": "NumeroProcesso", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_processo", "mensagem_erro": "O campo (NumeroProcesso) não tem um registro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2220"}, {"layout": "AutorizacaoLicencaAmbiental", "campo": "NumeroContrato", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_contrato", "mensagem_erro": "O campo (NumeroContrato) não tem um registro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2221"}, {"layout": "Convenios", "campo": "DataAssinatura", "regra": "DATE_BEFORE", "parametro": "DataCelebracao", "mensagem_erro": "É preciso que a data informada em (DataAssinatura) seja igual ou posterior à data de celebração (DataCelebracao).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2230"}, {"layout": "Convenios", "campo": "DataFimVigencia", "regra": "DATE_BEFORE", "parametro": "DataInicioVigencia", "mensagem_erro": "É preciso que a data informada em (DataFimVigencia) é anterior à data de início vigência (DataInicioVigencia).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2231"}, {"layout": "Convenios", "campo": "DataPublicacao", "regra": "DATE_BEFORE", "parametro": "DataAssinatura", "mensagem_erro": "É preciso que a data informada em (DataPublicacao) seja posterior à data informada em (DataAssinatura).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2232"}, {"layout": "Convenios", "campo": "DataInicioVigencia", "regra": "DATE_BEFORE", "parametro": "DataPublicacao", "mensagem_erro": "É preciso que a data informada em (DataInicioVigencia) seja posterior à data de publicação (DataPublicacao).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2233"}, {"layout": "Convenios", "campo": "ValorGlobal", "regra": "?", "parametro": "Se o valor do campo (ValorGlobal) não for a soma dos valores dos campos (ValorRepasse) + (ValorContrapartida)", "mensagem_erro": "O Valor Global do Convênio deve ser igual à soma dos Valores de Repasse + Contrapartida.", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2235"}, {"layout": "Convenios", "campo": "ValorGlobal", "regra": "CONDITIONAL_MANDATORY", "parametro": "Convenios,Modalidade,1|2|3|4|5", "mensagem_erro": "A Modalidade escolhida exige um Valor Global.", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2236"}, {"layout": "Convenios", "campo": "OutraModalidade", "regra": "CONDITIONAL_MANDATORY", "parametro": "<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,10", "mensagem_erro": "A Modalidade escolhida exige a informação do campo Outra Modalidade.", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2237"}, {"layout": "SituacaoConvenio", "campo": "NumeroConvenio", "regra": "FOREIGN_KEY", "parametro": "Convenios,numero_convenio", "mensagem_erro": "Não foi encontrada correspondência no leiaute Convênio (NumeroConvenio).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2240"}, {"layout": "SituacaoConvenio", "campo": "", "regra": "?", "parametro": "Se o último registro neste leiaute tiver a (Situacao) com valor 2-Cancelado, e ocorrer uma nova entrada de qualquer outro tipo.", "mensagem_erro": "O convênio está cancelado e não existem novas Situações após o cancelamento.", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2241"}, {"layout": "AditivoConvenio", "campo": "NumeroConvenio", "regra": "FOREIGN_KEY", "parametro": "Convenios,numero_convenio", "mensagem_erro": "Não foi encontrada correspondência no leiaute Convênio (NumeroConvenio).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2250"}, {"layout": "AditivoConvenio", "campo": "ValorGlobal", "regra": "?", "parametro": "Se o valor do campo (ValorGlobal) não for a soma dos valores dos campos (ValorRepasse) + (ValorContrapartida)", "mensagem_erro": "O Valor Global do Convênio deve ser igual à soma dos Valores de Repasse + Contrapartida.", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2252"}, {"layout": "AditivoConvenio", "campo": "DataFimVigencia", "regra": "?", "parametro": "Se o valor do campo (DataFimVigencia) for igual ou anterior ao valor do campo (DataFimVigencia) definido no leiaute (Convenio)", "mensagem_erro": "A data definida como Fim da Vigência do Aditivo deve ser posterior à data do Fim da Vigência original do Convênio.", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2253"}, {"layout": "Dependente", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2270"}, {"layout": "Cargo", "campo": "Percentual", "regra": "?", "parametro": "Se o campo (Percentual) receber qualquer valor o (TipoCargo) deve ser igual à 3.", "mensagem_erro": "Percentual de comissão serve apenas para cargos do tipo 3-Comissionado.", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2292"}, {"layout": "Cargo", "campo": "CodigoCarreira", "regra": "FOREIGN_KEY", "parametro": "Carreira,codigo", "mensagem_erro": "É preciso que o campo (CodigoCarreira) tenha um registro correspondente no leiaute (Carreira).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2293"}, {"layout": "ProgressaoCargo", "campo": "CodigoCargo", "regra": "FOREIGN_KEY", "parametro": "Cargo,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Cargo (CodigoCargo).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2300"}, {"layout": "ProgressaoCargo", "campo": "CodigoClasse", "regra": "FOREIGN_KEY", "parametro": "Classe,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Classe (CodigoClasse).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2301"}, {"layout": "ProgressaoCargo", "campo": "CodigoNivel", "regra": "FOREIGN_KEY", "parametro": "Nivel,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Nivel (CodigoNivel).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2302"}, {"layout": "FuncaoGratificadaCargoComissionado", "campo": "CodigoOrgao", "regra": "FOREIGN_KEY", "parametro": "Orgao,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Cargo (CodigoOrgao).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2310"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2320"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoOrgao", "regra": "FOREIGN_KEY", "parametro": "Orgao,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Orgao (CodigoOrgao).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2321"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoCarreira", "regra": "FOREIGN_KEY", "parametro": "Carreira,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Carreira (CodigoCarreira).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2322"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoCargo", "regra": "FOREIGN_KEY", "parametro": "Cargo,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Cargo (CodigoCargo).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2323"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "CodigoFG", "regra": "FOREIGN_KEY", "parametro": "FuncaoGratificadaCargoComissionado,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Cargo (FuncaoGratificadaCargoComissionado).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2325"}, {"layout": "Adicional", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2330"}, {"layout": "Adicional", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2332"}, {"layout": "Admis<PERSON><PERSON>", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2335"}, {"layout": "Admis<PERSON><PERSON>", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2337"}, {"layout": "Admis<PERSON><PERSON>", "campo": "CodigoOrgao", "regra": "FOREIGN_KEY", "parametro": "Orgao,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Orgao (CodigoOrgao).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2338"}, {"layout": "Admis<PERSON><PERSON>", "campo": "CodigoCarreira", "regra": "FOREIGN_KEY", "parametro": "Carreira,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Carreira (CodigoCarreira).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2339"}, {"layout": "Admis<PERSON><PERSON>", "campo": "CodigoCargo", "regra": "FOREIGN_KEY", "parametro": "Cargo,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Cargo (CodigoCargo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2340"}, {"layout": "AlteracaoJornadaDeTrabalho", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2350"}, {"layout": "AlteracaoJornadaDeTrabalho", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2351"}, {"layout": "AlteracaoJornadaDeTrabalho", "campo": "<PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "Carreira,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Carreira (CodigoCarreira).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2352"}, {"layout": "AlteracaoJornadaDeTrabalho", "campo": "Cargo", "regra": "FOREIGN_KEY", "parametro": "Cargo,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Cargo (CodigoCargo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2353"}, {"layout": "Cessao", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2355"}, {"layout": "Cessao", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2357"}, {"layout": "Disponibilidade", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2360"}, {"layout": "Disponibilidade", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2362"}, {"layout": "Desligamento", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2365"}, {"layout": "Desligamento", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2367"}, {"layout": "DesignacaoCargoComissaoFuncaoGratificada", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2370"}, {"layout": "DesignacaoCargoComissaoFuncaoGratificada", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2372"}, {"layout": "Licenca", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2375"}, {"layout": "Licenca", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2377"}, {"layout": "ProgressaoFuncional", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2390"}, {"layout": "ProgressaoFuncional", "campo": "ClasseAnterior", "regra": "FOREIGN_KEY", "parametro": "Classe,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Classe (ClasseAnterior).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2392"}, {"layout": "ProgressaoFuncional", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2393"}, {"layout": "Readaptacao", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2400"}, {"layout": "Readaptacao", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2401"}, {"layout": "Reconducao", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2405"}, {"layout": "Reconducao", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2406"}, {"layout": "Reintegracao", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2410"}, {"layout": "Reintegracao", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2412"}, {"layout": "Reenquadramento", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2415"}, {"layout": "Reenquadramento", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (Vinculo).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2417"}, {"layout": "ItemFolha", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute Servidor (CPF).", "impeditiva": true, "exercicio_inicial": "2022", "ativo": false, "numero_regra": "2420"}, {"layout": "ItemFolha", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "Não foi encontrada correspondencia no leiaute Vinculo (Matricula) .", "impeditiva": true, "exercicio_inicial": "2022", "numero_regra": "2422"}, {"layout": "MembroColegio", "campo": "CodigoGrupoColegiado", "regra": "FOREIGN_KEY", "parametro": "GruposColegiados,codigo", "mensagem_erro": "É preciso que o campo (CodigoGrupoColegiado) tenha um registro correspondente no leiaute (GruposColegiados).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2440"}, {"layout": "ResultadoAtuarial", "campo": "TipoFundo", "regra": "FOREIGN_KEY", "parametro": "PlanoCusteio,tipo_fundo", "mensagem_erro": "É preciso que o campo (TipoFundo) tenha um registro correspondente no leiaute (PlanoCusteio).", "impeditiva": false, "exercicio_inicial": "2024", "numero_regra": "2445"}, {"layout": "DependenteRPPS", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "Beneficiario,cpf", "mensagem_erro": "É preciso que o campo (CPF) tenha um registro correspondente no leiaute (Beneficiario).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2450"}, {"layout": "VinculoRPPS", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "Beneficiario,cpf", "mensagem_erro": "É preciso que o campo (CPF) tenha um registro correspondente no leiaute (Beneficiario).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2455"}, {"layout": "Pensionista", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "Beneficiario,cpf", "mensagem_erro": "É preciso que o campo (CPF) tenha um registro correspondente no leiaute (Beneficiario).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2460"}, {"layout": "Pensionista", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "Vin<PERSON>loRPPS,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (VinculoRPPS).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2461"}, {"layout": "AposentadoriaConcedida", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "Beneficiario,cpf", "mensagem_erro": "É preciso que o campo (CPF) tenha um registro correspondente no leiaute (Beneficiario).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2470"}, {"layout": "AposentadoriaConcedida", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "Vin<PERSON>loRPPS,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (VinculoRPPS).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2471"}, {"layout": "PensaoConcedida", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "Beneficiario,cpf", "mensagem_erro": "É preciso que o campo (CPF) tenha um registro correspondente no leiaute (Beneficiario).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2480"}, {"layout": "PensaoConcedida", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "Vin<PERSON>loRPPS,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (VinculoRPPS).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2481"}, {"layout": "ItemFolhaRPPS", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "Beneficiario,cpf", "mensagem_erro": "É preciso que o campo (CPF) tenha um registro correspondente no leiaute (Beneficiario).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2490"}, {"layout": "ItemFolhaRPPS", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "Vin<PERSON>loRPPS,matricula", "mensagem_erro": "É preciso que o campo (Matricula) tenha um registro correspondente no leiaute (VinculoRPPS).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2491"}, {"layout": "ParcelasParcelamento", "campo": "NumeroAcordo", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON>ela<PERSON>,numero_acordo", "mensagem_erro": "É preciso que o campo (NumeroAcordo) tenha um registro correspondente no leiaute (Parcelamento).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2500"}, {"layout": "CarteiraInvestimento", "campo": "<PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "PoliticaInvestimento,ano", "mensagem_erro": "Se não existir correspondente do campo (Ano) no leiaute (PoliticaInvestimento).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2505"}, {"layout": "AcompanhamentoMetaAtuarial", "campo": "<PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "PoliticaInvestimento,ano", "mensagem_erro": "Se não existir correspondente do campo (Ano) no leiaute (PoliticaInvestimento).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2510"}, {"layout": "ProfissionalSaude", "campo": "CNES", "regra": "FOREIGN_KEY", "parametro": "EstabelecimentoSaude,cnes", "mensagem_erro": "Não foi encontrada correspondência no leiaute (EstabelecimentoSaude).", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2540"}, {"layout": "EstabelecimentoLeito", "campo": "CNES", "regra": "FOREIGN_KEY", "parametro": "EstabelecimentoSaude,cnes", "mensagem_erro": "Não foi encontrada correspondência no leiaute EstabelecimentoSaude.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2545"}, {"layout": "EstabelecimentoEquipamento", "campo": "CNES", "regra": "FOREIGN_KEY", "parametro": "EstabelecimentoSaude,cnes", "mensagem_erro": "Não foi encontrada correspondência no leiaute EstabelecimentoSaude.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2550"}, {"layout": "FichaProgramacaoOrcamentaria", "campo": "CNES", "regra": "FOREIGN_KEY", "parametro": "EstabelecimentoSaude,cnes", "mensagem_erro": "Não foi encontrada correspondência no leiaute EstabelecimentoSaude.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2555"}, {"layout": "SolicitacaoProcedimentoAmbulatorial", "campo": "CNES", "regra": "FOREIGN_KEY", "parametro": "EstabelecimentoSaude,cnes", "mensagem_erro": "Não foi encontrada correspondência no leiaute EstabelecimentoSaude.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2560"}, {"layout": "AutorizacaoProcedimentoAmbulatorial", "campo": "CNES", "regra": "FOREIGN_KEY", "parametro": "EstabelecimentoSaude,cnes", "mensagem_erro": "Não foi encontrada correspondência no leiaute EstabelecimentoSaude.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2565"}, {"layout": "AutorizacaoInternacaoHospitalar", "campo": "CNES", "regra": "FOREIGN_KEY", "parametro": "EstabelecimentoSaude,cnes", "mensagem_erro": "Não foi encontrada correspondência no leiaute EstabelecimentoSaude.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2570"}, {"layout": "EquipamentoEscola", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2600"}, {"layout": "EquipamentoEscola", "campo": "Equipamento", "regra": "?", "parametro": "", "mensagem_erro": "É preciso que o campo (Equipamento) tenha um registro correspondente na Tabela 35 — Equipamentos de Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2601"}, {"layout": "EstruturaEscolar", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2605"}, {"layout": "EstruturaEscolar", "campo": "Estrutura", "regra": "?", "parametro": "", "mensagem_erro": "É preciso que o campo (Estrutura) tenha um registro correspondente na Tabela 36 — Estrutura de Escola", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2606"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2610"}, {"layout": "VinculoProfissionalEducacao", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2612"}, {"layout": "<PERSON><PERSON>", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2615"}, {"layout": "TurmaAluno", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2620"}, {"layout": "TurmaAluno", "campo": "CodigoTurma", "regra": "FOREIGN_KEY", "parametro": "Turma,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Turma.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2621"}, {"layout": "TurmaAluno", "campo": "Identificacao", "regra": "FOREIGN_KEY", "parametro": "Aluno,identificacao", "mensagem_erro": "Não foi encontrada correspondência no leiaute Aluno.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2622"}, {"layout": "TurmaProfissional", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2625"}, {"layout": "TurmaProfissional", "campo": "CodigoTurma", "regra": "FOREIGN_KEY", "parametro": "Turma,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Turma.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2626"}, {"layout": "TurmaProfissional", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "ProfissionalEducacao,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute ProfissionalEducacao.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2627"}, {"layout": "FaltasProfissionalEducacao", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2630"}, {"layout": "FaltasProfissionalEducacao", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "ProfissionalEducacao,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute ProfissionalEducacao.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2631"}, {"layout": "FaltasProfissionalEducacao", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "VinculoProfissionalEducacao, matricula", "mensagem_erro": "Não foi encontrada correspondência no leiaute VinculoProfissionalEducacao.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2632"}, {"layout": "CapacitacaoProfissionalEducacao", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "ProfissionalEducacao,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute ProfissionalEducacao.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2635"}, {"layout": "DespesaPorEscola", "campo": "INEP", "regra": "FOREIGN_KEY", "parametro": "Escola,inep", "mensagem_erro": "Não foi encontrada correspondência no leiaute Escola.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2640"}, {"layout": "DespesaPorEscola", "campo": "TipoDespesa", "regra": "?", "parametro": "", "mensagem_erro": "É preciso que o campo (Equipamento) tenha um registro correspondente na Tabela 37 — Tipos de Despesa Escolar", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2641"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "PoderOrgao", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar PoderOrgao é obrigatório. Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "600", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "PoderOrgao", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar PoderOrgao é obrigatório.", "impeditiva": true, "numero_regra": "601", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "FinanceiroPermanente", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar FinanceiroPermanente é obrigatório. Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "602", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "FinanceiroPermanente", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar FinanceiroPermanente é obrigatório.", "impeditiva": true, "numero_regra": "603", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "CodigoFonteRecursoProprio", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar CodigoFonteRecursoProprio é obrigatório. Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "606", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "CodigoFonteRecurso", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar CodigoFonteRecursoProprio é obrigatório.", "impeditiva": true, "numero_regra": "607", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "NaturezaReceita", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar NaturezaReceita é obrigatório. Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "610", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "NaturezaReceita", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar NaturezaReceita é obrigatório.", "impeditiva": true, "numero_regra": "611", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "NaturezaDespesa", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar NaturezaDespesa é obrigatório. Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "612", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "NaturezaDespesa", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar NaturezaDespesa é obrigatório.", "impeditiva": true, "numero_regra": "613", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "FuncaoSubfuncao", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar FuncaoSubfuncao é obrigatório. Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "616", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "FuncaoSubfuncao", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar FuncaoSubfuncao é obrigatório.", "impeditiva": true, "numero_regra": "617", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "AnoInscricao", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar AnoInscricao é obrigatório. Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "618", "exercicio_inicial": "2022", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "campo": "AnoInscricao", "regra": "INFORMACOES_COMPLEMENTARES", "parametro": "Conta", "mensagem_erro": "Para este lançamento contábil, a Informação Complementar AnoInscricao é obrigatório.", "impeditiva": true, "numero_regra": "619", "exercicio_inicial": "2025"}, {"layout": "Dependente", "campo": "NomeDependente", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, se<PERSON> 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.). Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "2271", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "Dependente", "campo": "NomeDependente", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, seção 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.).", "impeditiva": true, "numero_regra": "2271", "exercicio_inicial": "2025"}, {"layout": "Dependente", "campo": "NomeDependente", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, se<PERSON> 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.). Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "2269", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "Dependente", "campo": "NomeDependente", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, seção 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.).", "impeditiva": true, "numero_regra": "2269", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NomePai", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, se<PERSON> 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.). Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "2266", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NomePai", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, seção 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.).", "impeditiva": true, "numero_regra": "2266", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NomePai", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, se<PERSON> 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.). Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "2267", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "NomePai", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, seção 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.)", "impeditiva": true, "numero_regra": "2267", "exercicio_inicial": "2025"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "Nome", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, se<PERSON> 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.). Essa regra será Impeditiva à partir do Exercício 2025.", "impeditiva": false, "numero_regra": "2268", "exercicio_inicial": "2024", "exercicio_final": "2024"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "Nome", "regra": "NOME_PESSOA", "parametro": "", "mensagem_erro": "Nome fora dos padrões eSocial (versão S-1.3, seção 7.3.1). <PERSON><PERSON> <PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON><PERSON>, sem números ou caracteres especiais (ex.: !, @, #, $, etc.).", "impeditiva": true, "numero_regra": "2268", "exercicio_inicial": "2025"}, {"layout": "RestosPagar", "campo": "CodigoUnidadeOrcamentaria", "numero_regra": "1192", "regra": "FOREIGN_KEY", "parametro": "UnidadeOrcamentaria,codigo", "mensagem_erro": "É preciso que o campo (CodigoUnidadeOrcamentaria) tenha um registro correspondente no leiaute (UnidadeOrcamentaria).", "impeditiva": true, "exercicio_inicial": "2022"}, {"layout": "AgenteContratacao", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2009", "exercicio_inicial": "2022"}, {"layout": "AgenteContratacao", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "É necessário que o campo (Matricula) deste leiaute, tenha um registro <PERSON><PERSON> le<PERSON> (Vinculo).", "impeditiva": false, "numero_regra": "2010", "exercicio_inicial": "2022"}, {"layout": "ItemLicitacao", "campo": "NumeroLicitacao", "parametro": "Licitacao,numero_licitacao", "regra": "FOREIGN_KEY", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON> (Licitacao)", "impeditiva": true, "numero_regra": "2020", "exercicio_inicial": "2022"}, {"layout": "ItemLicitacao", "campo": "NumeroContratacao", "numero_regra": "2022", "parametro": "ContratacaoDireta,numero_contratacao", "regra": "FOREIGN_KEY", "mensagem_erro": "É necessário que o campo (NumeroContratacao) deste leiaute, tenha um registro correspondenteno le<PERSON> (ContratacaoDireta).", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "GrupoLicitacao", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2030", "exercicio_inicial": "2022"}, {"layout": "GrupoLicitacaoItem", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2035", "exercicio_inicial": "2022"}, {"layout": "GrupoLicitacaoItem", "campo": "NumeroItem", "numero_regra": "2036", "regra": "FOREIGN_KEY", "parametro": "ItemLicitacao,numero_item", "mensagem_erro": "É necessário que o campo (NumeroItem) deste leiaute, tenha um registro correspondent<PERSON> le<PERSON>(ItemLicitacao).", "impeditiva": true, "exercicio_inicial": "2022"}, {"layout": "GrupoLicitacaoItem", "campo": "NumeroGrupo", "numero_regra": "2037", "regra": "FOREIGN_KEY", "parametro": "GrupoLicitacao,numero_grupo", "mensagem_erro": "É necessário que o campo (NumeroGrupo) deste leiaute, tenha um registro correspondent<PERSON> le<PERSON>(GrupoLicitacao).", "impeditiva": true, "exercicio_inicial": "2024"}, {"layout": "OrgaoParticipante", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2040", "exercicio_inicial": "2022"}, {"layout": "OrgaoParticipanteItem", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2045", "exercicio_inicial": "2022"}, {"layout": "OrgaoParticipanteItem", "campo": "NumeroItem", "regra": "FOREIGN_KEY", "parametro": "ItemLicitacao,numero_item", "mensagem_erro": "É necessário que o campo (NumeroItem) deste leiaute, tenha um registro correspondent<PERSON> le<PERSON>(ItemLicitacao).", "impeditiva": true, "numero_regra": "2047", "exercicio_inicial": "2022"}, {"layout": "AdesaoRegistroDePrecos", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2050", "exercicio_inicial": "2022"}, {"layout": "AdjudicacaoLicitacao", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2060", "exercicio_inicial": "2022"}, {"layout": "ProponenteLicitacao", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2080", "exercicio_inicial": "2022"}, {"layout": "ProponenteLicitacao", "campo": "NumeroContratacao", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário que o campo (NumeroContratacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(ContratacaoDireta).", "impeditiva": true, "numero_regra": "2081", "exercicio_inicial": "2024"}, {"layout": "ProponenteLicitacaoItem", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2085", "exercicio_inicial": "2022"}, {"layout": "ProponenteLicitacaoItem", "campo": "NumeroContratacao", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário que o campo (NumeroContratacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(ContratacaoDireta).", "impeditiva": true, "numero_regra": "2088", "exercicio_inicial": "2024"}, {"layout": "ProponenteLicitacaoItem", "campo": "NumeroItem", "regra": "FOREIGN_KEY", "parametro": "ItemLicitacao,numero_item", "mensagem_erro": "É necessário que o campo (NumeroItem) deste leiaute, tenha um registro correspondent<PERSON> le<PERSON>(ItemLicitacao).", "impeditiva": true, "numero_regra": "2089", "exercicio_inicial": "2024"}, {"layout": "ItemAdjudicado", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "numero_regra": "2090", "exercicio_inicial": "2022"}, {"layout": "ItemAdjudicado", "campo": "NumeroItem", "regra": "FOREIGN_KEY", "parametro": "ProponenteLicitacaoItem,numero_item", "mensagem_erro": "O campo (NumeroItem) não tem umregistro correspondent<PERSON>(ProponenteLicitacaoItem).", "impeditiva": true, "numero_regra": "2091", "exercicio_inicial": "2022"}, {"layout": "GrupoAdjudicado", "campo": "NumeroLicitacao", "numero_regra": "2100", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "GrupoAdjudicado", "campo": "<PERSON><PERSON><PERSON>", "numero_regra": "2102", "regra": "FOREIGN_KEY", "parametro": "ProponenteLicitacao,codigo", "mensagem_erro": "O campo (Vencedor) não tem umregistro <PERSON><PERSON>(ProponenteLicitacao), campo (Codigo).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "AtaRegistroDePreco", "campo": "NumeroLicitacao", "numero_regra": "2110", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "exercicio_inicial": "2022", "exercicio_final": null}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "DataPosse", "regra": "DATE_RANGE", "parametro": "100,0", "mensagem_erro": "A data informada no campo (DataPosse) tem mais de 100 anos ou é uma data no futuro, verifique a data informada.", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2326"}, {"layout": "TurmaProfissional", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "VinculoProfissionalEducacao,matricula", "mensagem_erro": "Não foi encontrada correspondência no leiaute ProfissionalEducacao.", "impeditiva": true, "exercicio_inicial": "2024", "numero_regra": "2628"}, {"layout": "GrupoLicitacao", "campo": "NumeroContratacao", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário que o campo (NumeroContratacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(ContratacaoDireta).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2031"}, {"layout": "GrupoLicitacaoItem", "campo": "NumeroContratacao", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário que o campo (NumeroContratacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(ContratacaoDireta).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2038"}, {"layout": "OrgaoParticipanteGrupo", "campo": "NumeroLicitacao", "regra": "FOREIGN_KEY", "parametro": "Licitacao,numero_licitacao", "mensagem_erro": "É necessário que o campo (NumeroLicitacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(Licitacao).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2041"}, {"layout": "OrgaoParticipanteGrupo", "campo": "CNPJOrgaoParticipante", "regra": "FOREIGN_KEY", "parametro": "OrgaoParticipante,cnpj", "mensagem_erro": "Não foi encontrada correspondência do órgão participante (CNPJOrgaoParticipante). É necessário informar os dados do Órgão que participa.", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2042"}, {"layout": "OrgaoParticipanteGrupo", "campo": "NumeroGrupo", "regra": "FOREIGN_KEY", "parametro": "GrupoLicitacao,numero_grupo", "mensagem_erro": "É necessário que o campo (NumeroGrupo) deste leiaute, tenha um registro correspondent<PERSON> le<PERSON>(GrupoLicitacao).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2043"}, {"layout": "ProponenteLicitacaoItem", "campo": "NumeroGrupo", "regra": "FOREIGN_KEY", "parametro": "GrupoLicitacao,numero_grupo", "mensagem_erro": "É necessário que o campo (NumeroGrupo) deste leiaute, tenha um registro correspondent<PERSON> le<PERSON>(GrupoLicitacao).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2083"}, {"layout": "ItemAdjudicado", "campo": "NumeroContratacao", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário que o campo (NumeroContratacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(ContratacaoDireta).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2094"}, {"layout": "GrupoAdjudicado", "campo": "NumeroContratacao", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário que o campo (NumeroContratacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(ContratacaoDireta).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2104"}, {"layout": "OrdemServic<PERSON>", "campo": "NumeroProcesso", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_processo", "mensagem_erro": "O campo (NumeroProcesso) não tem umregistro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2191"}, {"layout": "CadastroNacionalObras", "campo": "NumeroProcesso", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_processo", "mensagem_erro": "O campo (NumeroProcesso) não tem umregistro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2196"}, {"layout": "Acompanhamento", "campo": "NumeroProcesso", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_processo", "mensagem_erro": "O campo (NumeroProcesso) não tem umregistro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2202"}, {"layout": "Medicao", "campo": "NumeroProcesso", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_processo", "mensagem_erro": "O campo (NumeroProcesso) não tem umregistro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2212"}, {"layout": "DocumentoResponsabilidadeTecnica", "campo": "NumeroProcesso", "regra": "FOREIGN_KEY", "parametro": "Obra,numero_processo", "mensagem_erro": "O campo (NumeroProcesso) não tem umregistro correspondente na tabela (Obra).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2216"}, {"layout": "EquipamentoUnidade", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2645"}, {"layout": "EstruturaUnidadeSocial", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2650"}, {"layout": "ProfissionalSocial", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute (Servidor).", "impeditiva": false, "exercicio_inicial": "2025", "numero_regra": "2655"}, {"layout": "ProfissionalSocial", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2656"}, {"layout": "ProfissionalSocial", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "Não foi encontrada correspondência no leiaute (Vinculo).", "impeditiva": false, "exercicio_inicial": "2025", "numero_regra": "2657"}, {"layout": "RegistrosUnidade", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2660"}, {"layout": "NovasFamilias", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2662"}, {"layout": "SCFV", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2664"}, {"layout": "CapacitacaoDivulgacao", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2666"}, {"layout": "UsuarioRede", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2668"}, {"layout": "Familia", "campo": "NumeroMDS", "regra": "FOREIGN_KEY", "parametro": "UnidadeSocial,numero_mds", "mensagem_erro": "Não foi encontrada correspondência no leiaute (UnidadeSocial).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2670"}, {"layout": "SetorOrgaoSegurancaPublica", "campo": "CodigoOrgaoSP", "regra": "FOREIGN_KEY", "parametro": "OrgaoSegurancaPublica,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute (OrgaoSegurancaPublica).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2675"}, {"layout": "ServidorSegurancaPublica", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute (Servidor).", "impeditiva": false, "exercicio_inicial": "2025", "numero_regra": "2680"}, {"layout": "ServidorSegurancaPublica", "campo": "CodigoOrgaoSP", "regra": "FOREIGN_KEY", "parametro": "OrgaoSegurancaPublica,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute (OrgaoSegurancaPublica).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2681"}, {"layout": "ServidorSegurancaPublica", "campo": "CodigoSetorSP", "regra": "FOREIGN_KEY", "parametro": "SetorOrgaoSegurancaPublica,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute (OrgaoSegurancaPublica).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2682"}, {"layout": "ServidorSegurancaPublica", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "Não foi encontrada correspondência no leiaute (Vinculo).", "impeditiva": false, "exercicio_inicial": "2025", "numero_regra": "2683"}, {"layout": "SetorDefesaCivil", "campo": "CodigoOrgaoDC", "regra": "FOREIGN_KEY", "parametro": "OrgaoDefesaCivil,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute (OrgaoDefesaCivil).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2685"}, {"layout": "ServidorDefesaCivil", "campo": "CPF", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,cpf", "mensagem_erro": "Não foi encontrada correspondência no leiaute (Servidor).", "impeditiva": false, "exercicio_inicial": "2025", "numero_regra": "2690"}, {"layout": "ServidorDefesaCivil", "campo": "CodigoOrgaoDC", "regra": "FOREIGN_KEY", "parametro": "OrgaoDefesaCivil,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute (OrgaoDefesaCivil).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2691"}, {"layout": "ServidorDefesaCivil", "campo": "CodigoSetorDC", "regra": "FOREIGN_KEY", "parametro": "SetorDefesaCivil,codigo_orgao_dc", "mensagem_erro": "Não foi encontrada correspondência no leiaute (SetorDefesaCivil).", "impeditiva": true, "exercicio_inicial": "2025", "numero_regra": "2692"}, {"layout": "ServidorDefesaCivil", "campo": "<PERSON><PERSON><PERSON>", "regra": "FOREIGN_KEY", "parametro": "<PERSON><PERSON><PERSON>,matricula", "mensagem_erro": "Não foi encontrada correspondência no leiaute (Vinculo).", "impeditiva": false, "exercicio_inicial": "2025", "numero_regra": "2693"}, {"layout": "ItemAdjudicado", "campo": "NumeroGrupo", "regra": "FOREIGN_KEY", "parametro": "GrupoAdjudicado,numero_grupo", "mensagem_erro": "O campo (NumeroGrupo) não tem umregistro <PERSON><PERSON>(GrupoAdjudicado).", "impeditiva": true, "numero_regra": "2093", "exercicio_inicial": "2024"}, {"layout": "<PERSON><PERSON>", "campo": "CodigoOrgao", "regra": "FOREIGN_KEY", "parametro": "Orgao,codigo", "mensagem_erro": "É preciso que o campo (CodigoOrgao) tenha um registro correspondente no leiaute (Orgao).", "impeditiva": true, "numero_regra": "2282", "exercicio_inicial": "2024"}, {"layout": "<PERSON><PERSON>", "campo": "CodigoCardapio", "regra": "FOREIGN_KEY", "parametro": "Cardapio,codigo", "mensagem_erro": "Não foi encontrada correspondência no leiaute Cardapio.", "impeditiva": true, "numero_regra": "2616", "exercicio_inicial": "2024"}, {"layout": "AdjudicacaoLicitacao", "campo": "NumeroContratacao", "regra": "FOREIGN_KEY", "parametro": "ContratacaoDireta,numero_contratacao", "mensagem_erro": "É necessário que o campo (NumeroContratacao) deste leiaute, tenha um registro correspondenteno le<PERSON>(ContratacaoDireta).", "impeditiva": true, "numero_regra": "2073", "exercicio_inicial": "2025"}, {"layout": "AditivoContrato", "campo": "DataPublicacao", "regra": "DATE_COMPARE", "parametro": "DataInicioVigencia", "mensagem_erro": "A data informada como início da vigência(DataInicioVigencia) do aditivo,é anterior à data de publicação(DataPublicacao).", "impeditiva": false, "numero_regra": "2155", "exercicio_inicial": "2024"}, {"layout": "AditivoContrato", "campo": "DataInicioVigencia", "regra": "DATE_COMPARE", "parametro": "DataFimVigencia", "mensagem_erro": "A data informada como(DataFimVigencia)do contrato, é anterior à data informada como(DataInicioVigencia).", "impeditiva": false, "numero_regra": "2156", "exercicio_inicial": "2024"}, {"layout": "<PERSON><PERSON><PERSON>", "campo": "Jornada", "regra": "CONDITIONAL_MANDATORY", "parametro": "<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,10|11", "mensagem_erro": "<PERSON><PERSON> (TipoVinculo) informado é obrigatório informar o valor do campo (Jornada). Apenas para (TipoVinculo) igual 10. Aposentado e 11. Pensionista a (Jornada) é opcional.", "impeditiva": false, "numero_regra": "2324", "exercicio_inicial": "2022"}]