<?php

namespace App\Services;

use App\Enums\Comunicacao\PrioridadeEnum;
use App\Enums\Comunicacao\StatusMensagemEnum;
use App\Enums\Comunicacao\StatusRespostaMensagemEnum;
use App\Models\Comunicacao\Mensagem;
use App\Models\Comunicacao\MensagemAnexo;
use App\Models\Comunicacao\MensagemRascunho;
use App\Models\Comunicacao\MensagemResposta;
use App\Models\Comunicacao\TipoMensagem;
use App\Models\UnidadeGestora;
use App\Notifications\NewMessageComunicacaoNotification;
use App\Notifications\ResponseMessageComunicacaoNotification;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

class ComunicacaoService
{
    public $validations;

    public function sendMensagem(array $validated)
    {
        $user = Auth::user()->id;
        $enviadoEm = Carbon::now();

        $unidadesGestoras = UnidadeGestora::getUnidadesGestorasPorGrupo($validated);
        $mensagens = [];

        foreach ($unidadesGestoras as $unidadeGestoraId) {
            $newMessageData = [
                'assunto' => $validated['assunto'],
                'unidade_gestora_id' => $unidadeGestoraId,
                'tipo_mensagem_id' => $validated['tipo_mensagem_id'],
                'prioridade' => $validated['prioridade'],
                'status' => isset($validated['status']) ? $validated['status'] : StatusMensagemEnum::NAO_LIDA,
                'descricao' => $validated['descricao'],
                'diretoria_id' => $validated['diretoria_id'],
                'enviado_em' => $enviadoEm,
                'user_id' => $user,
            ];

            if (! empty($validated['prazo_dias'])) {
                $newMessageData['prazo_dias'] = $validated['prazo_dias'];
            }

            if (! empty($validated['e_tce'])) {
                $newMessageData['e_tce'] = $validated['e_tce'];
            }

            if (! empty($validated['e_tce'])) {
                $newMessageData['e_tce'] = $validated['e_tce'];
            }

            if (! empty($validated['papel_usuario'])) {
                $newMessageData['papel_usuario'] = $validated['papel_usuario'];
            }

            $newMessage = Mensagem::create($newMessageData);

            foreach ($validated['anexos'] as $anexo) {
                MensagemAnexo::create([
                    'mensagem_id' => $newMessage->id,
                    'arquivo' => $anexo['arquivo'],
                    'descricao' => $anexo['descricao'],
                    'user_id' => $user,
                    'enviado_em' => $enviadoEm,
                ]);
            }

            if (! empty($validated['mensagem_rascunho_id'])) {
                $mensagemRascunhoId = $validated['mensagem_rascunho_id'];

                $mensagem = Mensagem::where('mensagem_rascunho_id', $mensagemRascunhoId)->first();

                if ($mensagem) {
                    $mensagem->anexos()->delete();
                    $mensagem->delete();
                }

                MensagemRascunho::where('id', $mensagemRascunhoId)->delete();
            }

            $mensagens[] = $newMessage;

            if ($newMessage->prioridade == PrioridadeEnum::URGENTE) {

                $notificationData = new NewMessageComunicacaoNotification($newMessage);

                $newMessage->unidadeGestora->gestor()->each(function ($gestor) use ($notificationData) {
                    $gestor->notify($notificationData);
                });

                $newMessage->unidadeGestora->controlador()->each(function ($controlador) use ($notificationData) {
                    $controlador->notify($notificationData);
                });

                $newMessage->unidadeGestora->responsavelTecnico()->each(function ($responsavelTecnico) use ($notificationData) {
                    $responsavelTecnico->notify($notificationData);
                });

            }

        }

        return $mensagens ?? [];
    }

    public function sendRascunho(array $validated)
    {
        $user = Auth::user()->id;
        $enviadoEm = Carbon::now();
        $mensagemRascunho = MensagemRascunho::updateOrCreate(
            [
                'id' => $validated['mensagem_rascunho_id'] ?? null,
            ],
            [
                'tipo_unidade_id' => json_encode($validated['tipo_unidade_id']),
                'esfera' => json_encode($validated['esfera']),
                'unidades_gestoras' => json_encode($validated['unidade_gestora_id']),
            ]
        );

        $messageData = [
            'assunto' => $validated['assunto'],
            'tipo_mensagem_id' => $validated['tipo_mensagem_id'],
            'prioridade' => $validated['prioridade'],
            'status' => isset($validated['status']) ? $validated['status'] : StatusMensagemEnum::NAO_LIDA,
            'descricao' => $validated['descricao'],
            'diretoria_id' => $validated['diretoria_id'],
            'enviado_em' => $enviadoEm,
            'user_id' => $user,
            'mensagem_rascunho_id' => $mensagemRascunho->id,
        ];

        if (! empty($validated['prazo_dias'])) {
            $messageData['prazo_dias'] = $validated['prazo_dias'];
        }

        if (! empty($validated['e_tce'])) {
            $messageData['e_tce'] = $validated['e_tce'];
        }

        if (! empty($validated['papel_usuario'])) {
            $messageData['papel_usuario'] = $validated['papel_usuario'];
        }

        if (! empty($validated['autosave'])) {
            $messageData['autosave'] = $validated['autosave'];
        }

        $criteria = [
            'mensagem_rascunho_id' => $mensagemRascunho->id ?? null, // Supondo que você tenha um ID para identificar o registro
        ];

        $newMessage = Mensagem::updateOrCreate($criteria, $messageData);

        foreach ($validated['anexos'] as $anexo) {
            MensagemAnexo::create([
                'mensagem_id' => $newMessage->id,
                'arquivo' => $anexo['arquivo'],
                'descricao' => $anexo['descricao'],
                'user_id' => $user,
                'enviado_em' => $enviadoEm,
            ]);
        }

        return $newMessage;
    }

    /**
     * Autosaves a draft message.
     *
     * @param  array  $validated  The validated data for the draft message.
     * @return \App\Models\Comunicacao\Mensagem The saved message.
     */
    public function autosaveRascunho(array $validated, MensagemRascunho $mensagemRascunho): Mensagem
    {
        $newMessage = Mensagem::where('mensagem_rascunho_id', $mensagemRascunho->id)->first();
        $newMessage->update(['descricao' => $validated['text']]);

        return $newMessage;
    }

    public function listAnexos($mensagemId, array $filtros = [])
    {
        $query = MensagemAnexo::join('users', 'users.id', '=', 'comunicacao.mensagem_anexo.user_id')
            ->select('comunicacao.mensagem_anexo.*', 'users.name as user_name')
            ->where('mensagem_id', $mensagemId);

        if (isset($filtros['sortBy'])) {
            foreach ($filtros['sortBy'] as $sortBy) {
                $query->orderBy($sortBy['key'], $sortBy['order']);
            }
        } else {
            $query->orderBy('enviado_em', 'asc');
        }

        if (isset($filtros['itemsPerPage'])) {
            return $query->paginate($filtros['itemsPerPage']);
        }

        return $query->paginate(10);
    }

    public function listRespostasEAnexos($mensagemId)
    {
        $mensagem = Mensagem::with(['user', 'respostas.remetente', 'anexosComDeletados.user'])->findOrFail($mensagemId);

        $mensagemData = [
            'id' => $mensagem->id,
            'assunto' => $mensagem->assunto,
            'descricao' => $mensagem->descricao,
            'enviado_em' => $mensagem->enviado_em,
            'status' => StatusMensagemEnum::getDescription($mensagem->status),
            'user_id' => $mensagem->user_id,
            'user_name' => $mensagem->user->name,
        ];

        $respostas = $mensagem->respostas
            ->filter(function ($resposta) {
                return ! $resposta->autosave && $resposta->status === StatusRespostaMensagemEnum::ENVIADO;
            })
            ->transform(function ($resposta) {
                $status = StatusRespostaMensagemEnum::getDescription($resposta->status) ?? null;

                return [
                    'id' => $resposta->id,
                    'mensagem_id' => $resposta->mensagem_id,
                    'user_id' => $resposta->user_id,
                    'descricao' => $resposta->descricao,
                    'lido' => $resposta->lido,
                    'enviado_em' => $resposta->enviado_em,
                    'status' => $status,
                    'user_name' => $resposta->remetente->name,
                ];
            });

        $anexosComDeletados = $mensagem->anexosComDeletados->transform(function ($anexo) {
            return [
                'id' => $anexo->id,
                'mensagem_id' => $anexo->mensagem_id,
                'user_id' => $anexo->user_id,
                'arquivo' => $anexo->arquivo,
                'descricao' => $anexo->descricao,
                'enviado_em' => $anexo->enviado_em,
                'status' => $anexo->status,
                'user_name' => $anexo->user->name,
                'deleted_at' => $anexo->deleted_at,
            ];
        });

        $resultado = collect([$mensagemData])
            ->concat($respostas)
            ->concat($anexosComDeletados)
            ->sortBy('enviado_em')
            ->values();

        return $resultado;
    }

    public function listRespotaAutosave($mensagem)
    {
        $userId = Auth::user()->id;
        $resposta = MensagemResposta::firstOrCreate(
            [
                'mensagem_id' => $mensagem->id,
                'user_id' => $userId,
                'status' => StatusRespostaMensagemEnum::RASCUNHO,
                'autosave' => true,
            ],
            [
                'descricao' => '',
                'lido' => false,
                'enviado_em' => Carbon::now(),
            ]
        );

        return $resposta->toArray();
    }

    public function marcarRespostaComoLida(Mensagem $mensagem, MensagemResposta $resposta): bool
    {
        $mensagem->load('tipoMensagem');

        if (empty($mensagem->tipoMensagem->permite_resposta)) {
            $mensagem->status = StatusMensagemEnum::FECHADO;
            $mensagem->save();
        }

        $resposta->lido = true;

        return $resposta->save();
    }

    public function marcarMensagemComoLida(Mensagem $mensagem): Mensagem
    {
        $mensagem->load('tipoMensagem');

        if (empty($mensagem->tipoMensagem->permite_resposta)) {
            $mensagem->status = StatusMensagemEnum::FECHADO;
        }

        $mensagem->lido = true;
        $mensagem->save();

        return $mensagem;
    }

    public function deleteResposta(MensagemResposta $resposta): bool
    {
        return $resposta->delete();
    }

    public function finalizeMensagem(Mensagem $mensagem)
    {
        $mensagem->status = StatusMensagemEnum::FECHADO;

        return $mensagem->save();
    }

    public function updateResposta(MensagemResposta $resposta, string $description): MensagemResposta
    {
        $emissionDate = Carbon::now();
        $user = Auth::user()->id;
        $resposta->update(
            [
                'descricao' => $description,
                'enviado_em' => $emissionDate,
                'user_id' => $user,
            ]
        );

        return $resposta;
    }

    public function uploadFile(UploadedFile $anexo)
    {
        $uploadPath = 'anexos';
        $fileName = time().'_'.$anexo->getClientOriginalName();
        $anexo->storeAs($uploadPath, $fileName, 'comunicacao');

        return $fileName;
    }

    public function deletaArquivoTemporario(string $arquivo): bool
    {
        $caminhoArquivo = 'anexos/'.$arquivo;
        if (Storage::disk('comunicacao')->exists($caminhoArquivo)) {
            Storage::disk('comunicacao')->delete($caminhoArquivo);

            return true;
        }

        return false;
    }

    public function storeAnexo(Mensagem $mensagem, string $anexo, string $descricao): MensagemAnexo
    {
        $user = Auth::user()->id;
        $mensagemAnexo = MensagemAnexo::create([
            'mensagem_id' => $mensagem->id,
            'arquivo' => $anexo,
            'descricao' => $descricao,
            'user_id' => $user,
            'enviado_em' => Carbon::now(),
        ]);

        return $mensagemAnexo;
    }

    public function sendResposta(Mensagem $mensagem, $validated): MensagemResposta
    {
        if (
            (
                $mensagem->respostas->isEmpty()
                    || ($mensagem->respostas->first()
                        && $mensagem->respostas->first()->status == StatusRespostaMensagemEnum::RASCUNHO
                    )
                && $mensagem->status == StatusMensagemEnum::NAO_LIDA
            )
        ) {
            $mensagem->status = StatusMensagemEnum::ANDAMENTO;
            $mensagem->save();
        }

        $userId = Auth::user()->id;

        $resposta = MensagemResposta::updateOrCreate(
            [
                'mensagem_id' => $mensagem->id,
                'user_id' => $userId,
                'status' => StatusRespostaMensagemEnum::RASCUNHO,
                'autosave' => true,
            ],
            [
                'descricao' => $validated['descricao'],
                'lido' => false,
                'enviado_em' => Carbon::now(),
                'status' => StatusRespostaMensagemEnum::ENVIADO,
                'autosave' => false,
            ]
        );

        $notificationData = new ResponseMessageComunicacaoNotification($resposta);

        if ($resposta->mensagem->prioridade == PrioridadeEnum::URGENTE) {
            $resposta->mensagem->unidadeGestora->gestor()->each(function ($gestor) use ($notificationData) {
                $gestor->notify($notificationData);
            });

            $resposta->mensagem->unidadeGestora->controlador()->each(function ($controlador) use ($notificationData) {
                $controlador->notify($notificationData);
            });

            $resposta->mensagem->unidadeGestora->responsavelTecnico()->each(function ($responsavelTecnico) use ($notificationData) {
                $responsavelTecnico->notify($notificationData);
            });
        }

        return $resposta;
    }

    public function autosaveResposta(Mensagem $mensagem, MensagemResposta $mensagemResposta, $validated): MensagemResposta
    {
        $mensagemResposta->update(
            [
                'descricao' => $validated['text'],
                'enviado_em' => Carbon::now(),
                'status' => StatusRespostaMensagemEnum::RASCUNHO,
                'autosave' => true,
            ]
        );

        return $mensagemResposta;
    }

    public function listPrioridades()
    {
        return array_map(function ($prioridade) {
            return [
                'key' => $prioridade,
                'value' => PrioridadeEnum::getDescription($prioridade),
            ];
        }, PrioridadeEnum::getValues());
    }

    public function listTipoMensagem()
    {
        return TipoMensagem::all();
    }

    public function listMensagemStatuses()
    {
        return array_map(function ($statuses) {
            return [
                'key' => $statuses,
                'value' => StatusMensagemEnum::getDescription($statuses),
            ];
        }, StatusMensagemEnum::getValues());

    }

    public function draftResposta(MensagemResposta $resposta, string $descricao): MensagemResposta
    {
        $emissionDate = Carbon::now();
        $user = Auth::user()->id;

        $resposta->update(
            [
                'enviado_em' => $emissionDate,
                'user_id' => $user,
                'descricao' => $descricao,
                'status' => StatusRespostaMensagemEnum::RASCUNHO,
            ]
        );

        return $resposta;
    }

    public function fetchMensagens(array $filtros = [])
    {
        $user = Auth::user();

        $query = Mensagem::join('comunicacao.tipo_mensagem', 'comunicacao.tipo_mensagem.id', '=', 'mensagem.tipo_mensagem_id')
            ->leftJoin('unidade_gestoras', 'unidade_gestoras.id', '=', 'mensagem.unidade_gestora_id')
            ->leftJoin('cidades', 'cidades.id', '=', 'unidade_gestoras.cidade_id')
            ->join('users', 'users.id', '=', 'comunicacao.mensagem.user_id')
            ->select('mensagem.*', 'comunicacao.tipo_mensagem.nome as tipo_mensagem_nome', 'unidade_gestoras.nome as unidade_gestora_nome', 'cidades.nome as cidade_nome', 'users.name as user_name');

        if (isset($filtros['numero_mensagem']) && ! empty($filtros['numero_mensagem'])) {
            $query->whereIn('comunicacao.mensagem.id', $filtros['numero_mensagem']);
        }

        if (isset($filtros['tipo_mensagem_id']) && ! empty($filtros['tipo_mensagem_id'])) {
            $query->whereIn('comunicacao.mensagem.tipo_mensagem_id', $filtros['tipo_mensagem_id']);
        }

        if (isset($filtros['unidade_gestora_id']) && ! empty($filtros['unidade_gestora_id'])) {
            $query->whereIn('mensagem.unidade_gestora_id', $filtros['unidade_gestora_id']);
        }

        if (isset($filtros['user_id']) && ! empty($filtros['user_id'])) {
            $query->whereIn('comunicacao.mensagem.user_id', $filtros['user_id']);
        }

        if (isset($filtros['status']) && ! empty($filtros['status'])) {
            $query->whereIn('comunicacao.mensagem.status', $filtros['status']);
        }

        if (isset($filtros['e_tce']) && ! empty($filtros['e_tce'])) {
            $query->where('mensagem.e_tce', $filtros['e_tce']);
        }

        if (isset($filtros['data_inicio']) || isset($filtros['data_fim'])) {
            $query->whereNotNull('mensagem.prazo_dias');

            if (isset($filtros['data_inicio'])) {
                $dataInicio = isset($filtros['data_inicio']) ? $filtros['data_inicio'].' 00:00:00' : Carbon::today()->startOfDay()->toDateTimeString();
                $query->where(DB::raw("mensagem.enviado_em + (mensagem.prazo_dias || ' days')::interval"), '>=', $dataInicio);
            }
            if (isset($filtros['data_fim'])) {
                $dataFim = isset($filtros['data_fim']) ? $filtros['data_fim'].' 23:59:59' : Carbon::today()->endOfDay()->toDateTimeString();
                $query->where(DB::raw("mensagem.enviado_em + (mensagem.prazo_dias || ' days')::interval"), '<=', $dataFim);
            }
        }

        if (isset($filtros['prioridade']) && ! empty($filtros['prioridade'])) {
            $query->whereIn('mensagem.prioridade', $filtros['prioridade']);
        }

        if (isset($filtros['diretorias']) && ! empty($filtros['diretorias'])) {
            $query->whereIn('mensagem.diretoria_id', $filtros['diretorias']);
        }

        if (isset($filtros['assunto'])) {
            $query->where('mensagem.assunto', 'ILIKE', '%'.$filtros['assunto'].'%');
        }

        if (isset($filtros['descricao'])) {
            $query->where('mensagem.descricao', 'ILIKE', '%'.$filtros['descricao'].'%');
        }

        if (isset($filtros['prazo_dias'])) {
            $query->where('mensagem.prazo_dias', $filtros['prazo_dias']);
        }

        if (isset($filtros['municipios']) && ! empty($filtros['municipios'])) {
            $query = $query->whereHas('unidadeGestora', function ($query) use ($filtros) {
                $query->whereIn('unidade_gestoras.cidade_id', $filtros['municipios']);
            });
        }

        if (isset($filtros['tipo_ug']) && ! empty($filtros['tipo_ug'])) {
            $query = $query->whereHas('unidadeGestora', function ($query) use ($filtros) {
                $query->whereIn('unidade_gestoras.tipo_unidade', $filtros['tipo_ug']);
            });
        }

        if (isset($filtros['rascunho']) && ! empty($filtros['rascunho'])) {
            $query->whereNotNull('mensagem.mensagem_rascunho_id');
        }

        if (isset($filtros['sortBy'])) {
            foreach ($filtros['sortBy'] as $sortBy) {
                $query->orderBy($sortBy['key'], $sortBy['order']);
            }
        }

        if ($user->isControlador() || $user->isGestor()) {
            $unidadesIds = $user->unidadesGestoras()->pluck('unidade_gestoras.id');
            $query->whereIn('mensagem.unidade_gestora_id', $unidadesIds);

            $papelUsuario = $user->role->value;

            $query->where(function ($query) use ($papelUsuario) {
                $query->orWhere(function ($query) use ($papelUsuario) {
                    $query->whereJsonContains('mensagem.papel_usuario', $papelUsuario);
                });
                $query->orWhereNull('mensagem.papel_usuario');
            });
        }

        if (isset($filtros['itemsPerPage'])) {
            $resultados = $query->paginate($filtros['itemsPerPage']);
            $resultados->getCollection()->transform(function ($mensagem) {
                $mensagem->prioridadeValue = PrioridadeEnum::getDescription($mensagem->prioridade);
                $mensagem->status = StatusMensagemEnum::getDescription($mensagem->status);

                return $mensagem;
            });
        } else {
            $resultados = $query->get();
            $resultados->transform(function ($mensagem) {
                $mensagem->prioridadeValue = PrioridadeEnum::getDescription($mensagem->prioridade);
                $mensagem->status = StatusMensagemEnum::getDescription($mensagem->status);

                return $mensagem;
            });
        }

        return $resultados;
    }

    public function getZipAnexosFiles(Mensagem $mensagem)
    {
        $anexos = $mensagem->anexos;
        $zip = new ZipArchive;

        $zipFilePath = 'anexos/'.$mensagem->id.'.zip';
        $zipFullPath = Storage::disk('comunicacao')->path($zipFilePath);

        if ($zip->open($zipFullPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            throw new \Exception('Não foi possível criar o arquivo ZIP.');
        }

        foreach ($anexos as $anexo) {
            $this->addAnexoToZip($zip, $anexo);
        }

        $zip->close();

        return $zipFullPath;
    }

    private function addAnexoToZip(ZipArchive $zip, MensagemAnexo $anexo)
    {
        $filePath = 'anexos/'.$anexo->arquivo;
        $fileFullPath = Storage::disk('comunicacao')->path($filePath);

        if (! Storage::disk('comunicacao')->exists($filePath)) {
            throw new \Exception('Arquivo não encontrado: '.$filePath);
        }

        if (! $zip->addFile($fileFullPath, $anexo->arquivo)) {
            throw new \Exception('Não foi possível adicionar o arquivo ao ZIP: '.$fileFullPath);
        }
    }

    public function listDetalheMensagem($mensagemId)
    {
        $mensagem = Mensagem::with('rascunho')
            ->select(
                'mensagem.mensagem_rascunho_id',
                'mensagem.diretoria_id',
                'mensagem.prazo_dias',
                'mensagem.tipo_mensagem_id',
                'mensagem.lido',
                'unidade_gestoras.nome as unidade_gestora_nome',
                'mensagem.prioridade',
                'comunicacao.tipo_mensagem.nome as tipo_mensagem',
                'comunicacao.tipo_mensagem.permite_respostas',
                'mensagem.e_tce as protocolo_e_tce',
                'mensagem.assunto',
                'diretorias.nome as diretoria_nome',
                'mensagem.status',
                'mensagem.descricao',
                'mensagem.enviado_em',
                'cidades.nome as cidade_nome'
            )
            ->join('comunicacao.tipo_mensagem', 'comunicacao.tipo_mensagem.id', '=', 'mensagem.tipo_mensagem_id')
            ->leftJoin('unidade_gestoras', 'unidade_gestoras.id', '=', 'mensagem.unidade_gestora_id')
            ->leftJoin('cidades', 'cidades.id', '=', 'unidade_gestoras.cidade_id')
            ->join('diretorias', 'diretorias.id', '=', 'mensagem.diretoria_id')
            ->where('mensagem.id', $mensagemId)
            ->first();

        if ($mensagem) {
            $mensagem->prioridadeValue = PrioridadeEnum::getDescription($mensagem->prioridade);
            $mensagem->status = StatusMensagemEnum::getDescription($mensagem->status);
        }

        return $mensagem;

    }

    public function deleteAnexo(MensagemAnexo $anexo): bool
    {
        $filePath = 'anexos/'.$anexo->arquivo;
        Storage::disk('comunicacao')->delete($filePath);

        return $anexo->delete();
    }
}
