<?php

namespace App\Nova;

use App\Enums\Esfera;
use App\Models\Cidade;
use App\Nova\Filters\ExercicioFilter;
use App\Nova\Filters\sapc\CertidoesPvl\AnalisadoFilter;
use App\Nova\Filters\sapc\CertidoesPvl\CidadeFilter;
use App\Nova\Filters\sapc\CertidoesPvl\EsferaFilter;
use App\Traits\Utils;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource;
use Outl1ne\NovaDetachedFilters\HasDetachedFilters;
use Outl1ne\NovaDetachedFilters\NovaDetachedFilters;
use SimpleSquid\Nova\Fields\Enum\Enum;

class CidadeExercicioAnalisado extends Resource
{
    use HasDetachedFilters, Utils;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Sapc\CidadeExercicioAnalisado>
     */
    public static $model = \App\Models\Sapc\CidadeExercicioAnalisado::class;

    public static $group = 'e-Contas';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'exercicio';

    public static function label()
    {
        return ' Exercícios Analisados';
    }

    public static function singularLabel()
    {
        return 'Exercício Analisado';
    }

    public function authorizedToReplicate($request)
    {
        return false;
    }

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'exercicio', 'cidade.nome',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Exercício', 'exercicio')
                ->options($this->listExercisesSince(2018))
                ->sortable()
                ->rules([
                    'required',
                    'integer',
                    function ($attribute, $value, $fail) use ($request) {
                        $model = $this->resource;

                        if ($request->isUpdateOrUpdateAttachedRequest() && $model->exercicio == $value) {
                            return;
                        }

                        $existe = \App\Models\Sapc\CidadeExercicioAnalisado::where('exercicio', $value)
                            ->where('cidade_id', $request->input('cidade_id'))
                            ->where('id', '!=', $model->id)
                            ->exists();

                        if ($existe) {
                            $fail('O exercício já foi cadastrado para esta cidade.');
                        }
                    },
                ]),

            Select::make('Cidade', 'cidade_id')
                ->options(Cidade::orderBy('nome')->pluck('nome', 'id')->toArray())
                ->displayUsingLabels()
                ->sortable()
                ->searchable()
                ->rules([
                    'required',
                    function ($attribute, $value, $fail) use ($request) {
                        $model = $this->resource;

                        if ($request->isUpdateOrUpdateAttachedRequest() && $model->cidade_id == $value) {
                            return;
                        }

                        $existe = \App\Models\Sapc\CidadeExercicioAnalisado::where('cidade_id', $value)
                            ->where('exercicio', $request->input('exercicio'))
                            ->where('id', '!=', $model->id)
                            ->exists();

                        if ($existe) {
                            $fail('Esta cidade já possui um registro para o exercício selecionado.');
                        }
                    },
                ]),

            Boolean::make('Analisado', 'analisado')
                ->sortable()
                ->trueValue(1)
                ->falseValue(0),

            Select::make('Esfera', 'esfera')
                ->options([
                    Esfera::Municipal => 'Municipal',
                    Esfera::Estadual => 'Estadual',
                ])
                ->onlyOnForms()
                ->displayUsingLabels()
                ->sortable()
                ->rules('required'),

            Enum::make('Esfera', 'esfera')
                ->attach(Esfera::class)
                ->hideWhenCreating()
                ->hideWhenUpdating()
                ->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new NovaDetachedFilters([
                (new ExercicioFilter)->withMeta(['width' => 'w-1/4']),
                (new CidadeFilter)->withMeta(['width' => 'w-1/4']),
                (new AnalisadoFilter)->withMeta(['width' => 'w-1/4']),
                (new EsferaFilter)->withMeta(['width' => 'w-1/4']),
            ]))->width('full'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function redirectAfterCreate(NovaRequest $request, $resource)
    {
        return '/resources/cidade-exercicio-analisados';
    }

    public static function redirectAfterUpdate(NovaRequest $request, $resource)
    {
        return '/resources/cidade-exercicio-analisados';
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        $query->getQuery()->orders = [];

        return $query->orderBy(
            Cidade::select('nome')
                ->whereColumn('cidades.id', 'cidade_exercicio_analisado.cidade_id')
                ->limit(1),
            'asc'
        )
            ->orderBy('exercicio', 'asc');
    }
}
