<?php

namespace App\Nova\Sapc;

use App\Models\Sapc\ModeloAnaliseFormulario as ModeloAnaliseFormularioModel;
use App\Nova\Resource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Http\Requests\NovaRequest;

class ModeloAnaliseFormulario extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Sapc\ModeloAnaliseFormulario>
     */
    public static $model = \App\Models\Sapc\ModeloAnaliseFormulario::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    public static $group = 'e-Contas';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'modelo_analise_id',
        'modelo_formulario_id',
        'ordenacao',
        'data_inicio',
        'data_fim',
    ];

    public static $displayInNavigation = false;

    public static function uriKey()
    {
        return 'modelos-analises-formularios';
    }

    public static function label()
    {
        return 'Modelos análises formulários';
    }

    public static function singularLabel()
    {
        return 'Modelo análise formulário';
    }

    public static function createButtonLabel()
    {
        return __('Anexar Formulário em Análise');
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $modeloAnaliseId = $request->input('viaResourceId');
        $viaResource = $request->input('viaResource');

        return [
            ID::make()->sortable(),
            BelongsTo::make(__('Modelo Análise'), 'modeloAnalise', ModeloAnalise::class)
                ->relatableQueryUsing(function (NovaRequest $request, Builder $query) use ($modeloAnaliseId, $viaResource) {
                    if ($request->isUpdateOrUpdateAttachedRequest()) {
                        return;
                    }

                    if ($viaResource == 'modelos-analises') {
                        $query->where('id', $modeloAnaliseId);
                    }

                    if ($viaResource == 'modelos-formularios') {
                        if ($modeloAnaliseId) {
                            $excludedModeloFormularioIds = ModeloAnaliseFormularioModel::where('modelo_formulario_id', $modeloAnaliseId)->pluck('modelo_analise_id');

                            if ($excludedModeloFormularioIds->isNotEmpty()) {
                                $query->whereNotIn('id', $excludedModeloFormularioIds);
                            }
                        }
                    }
                })
                ->required(),

            BelongsTo::make(__('Modelo Formulário'), 'modeloFormulario', ModeloFormulario::class)
                ->relatableQueryUsing(function (NovaRequest $request, Builder $query) use ($modeloAnaliseId, $viaResource) {
                    if ($request->isUpdateOrUpdateAttachedRequest()) {
                        return;
                    }

                    if ($viaResource == 'modelos-formularios') {
                        $query->where('id', $modeloAnaliseId);
                    }

                    if ($viaResource == 'modelos-analises') {
                        $query->where('status', 'ativo');
                        if ($modeloAnaliseId) {
                            $excludedModeloFormularioIds = ModeloAnaliseFormularioModel::where('modelo_analise_id', $modeloAnaliseId)->pluck('modelo_formulario_id');
                            if ($excludedModeloFormularioIds->isNotEmpty()) {
                                $query->whereNotIn('id', $excludedModeloFormularioIds);
                            }
                        }
                    }
                })
                ->rules('required', function ($attribute, $modeloFormularioId, $fail) use ($request, $modeloAnaliseId) {
                    $modeloAnaliseQuery = ModeloAnaliseFormularioModel::query()
                        ->where('modelo_analise_id', $modeloAnaliseId)
                        ->where('modelo_formulario_id', $modeloFormularioId);

                    if ($request->isCreateOrAttachRequest() && $modeloAnaliseQuery->exists()) {
                        return $fail(__('Este Modelo de Formulário já está vinculado ao Modelo de análise formulário.'));
                    }

                    $existingEntry = $modeloAnaliseQuery->first();

                    if ($existingEntry && $existingEntry->id != $request->resourceId) {
                        return $fail(__('Este Modelo de Formulário já está vinculado ao Modelo de análise formulário.'));
                    }
                }),
            Date::make('Data Inicio', 'data_inicio')->rules('required'),
            Date::make('Data Fim', 'data_fim'),
            Number::make('Ordem', 'ordenacao')->rules('required'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }

    public static function redirectAfterCreate(NovaRequest $request, $resource)
    {
        $analiseFormulario = ModeloAnaliseFormularioModel::query()->find($resource->id);

        return '/resources/modelos-analises/'.$analiseFormulario->modelo_analise_id;
    }

    public static function redirectAfterUpdate(NovaRequest $request, $resource)
    {
        $analiseFormulario = ModeloAnaliseFormularioModel::query()->find($resource->id);

        return '/resources/modelos-analises/'.$analiseFormulario->modelo_analise_id;
    }
}
