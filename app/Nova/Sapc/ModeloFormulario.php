<?php

namespace App\Nova\Sapc;

use App\Enums\Sapc\CategoriaDocumento;
use App\Enums\Sapc\CertidaoPVLTipoModeloFormularioEnum;
use App\Enums\Sapc\StatusModeloFormulario;
use App\Nova\Filters\EnumFilter;
use App\Nova\Resource;
use Bradoctech\SapcNovaCkEditor\CkEditor;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\File;
use Laravel\Nova\Fields\FormData;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Outl1ne\NovaDetachedFilters\HasDetachedFilters;
use Outl1ne\NovaDetachedFilters\NovaDetachedFilters;
use SimpleSquid\Nova\Fields\Enum\Enum;

class ModeloFormulario extends Resource
{
    use HasDetachedFilters;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Sapc\ModeloFormulario>
     */
    public static $model = \App\Models\Sapc\ModeloFormulario::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'nome';

    public static $group = 'e-Contas';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'nome', 'texto', 'versao', 'status',
    ];

    public static function uriKey()
    {
        return 'modelos-formularios';
    }

    public static function label()
    {
        return 'Modelos de formulários';
    }

    public static function singularLabel()
    {
        return 'modelo de formulário';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $categoriaDocumentoOptions = [];
        foreach (CategoriaDocumento::asArray() as $key) {
            $categoriaDocumentoOptions[$key] = CategoriaDocumento::getDescription($key);
        }

        return [
            ID::make()->sortable(),
            Text::make(__('Nome'), 'nome')->rules(['required'])->sortable(),
            Enum::make(__('Tipo de Formulário'), 'tipo_formulario')
                ->attach(CertidaoPVLTipoModeloFormularioEnum::class)
                ->displayUsingLabels()
                ->default(CertidaoPVLTipoModeloFormularioEnum::CONTEUDO)
                ->sortable(),

            Select::make('Categoria', 'categoria_documento')
                ->options($categoriaDocumentoOptions)
                ->sortable()
                ->displayUsing(fn ($modelo) => CategoriaDocumento::getDescription($modelo)),

            CkEditor::make(trans('Texto'), 'texto')->stacked()->hideFromIndex()->rules(function ($request) {
                return $request->input('tipo_formulario') !== 'marcacao' ? ['required'] : [];
            }),

            Number::make(__('Versão'), 'versao')->readonly()->default('1')->hideFromIndex()->hideWhenCreating()->hideWhenUpdating()->sortable(),
            Enum::make(__('Status'))->attach(StatusModeloFormulario::class)->displayUsingLabels()->sortable(),

            // HasMany::make(__('Modelos formulário'), 'analisesFormularios', ModeloFormulario::class),
            HasMany::make(__('Modelos Análise'), 'modelosAnalisesFormularios', ModeloAnaliseFormulario::class),

            Boolean::make('Capa?', 'capa')
                ->sortable(),

            File::make('Arquivo de capa', 'arquivo_capa')
                ->hideFromIndex()
                ->dependsOn(
                    ['capa'],
                    function (File $field, NovaRequest $request, FormData $formData) {
                        if (! $formData->capa) {
                            $field->hide();
                        }
                    }
                ),

            Boolean::make('ContraCapa?', 'contracapa')
                ->sortable(),

            File::make('Arquivo de Contracapa', 'arquivo_contracapa')
                ->hideFromIndex()
                ->dependsOn(
                    ['contracapa'],
                    function (File $field, NovaRequest $request, FormData $formData) {
                        if (! $formData->contracapa) {
                            $field->hide();
                        }
                    }
                ),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new NovaDetachedFilters([
                (new EnumFilter('status', 'App\Enums\Sapc\StatusModeloFormulario'))->withMeta(['width' => 'w-1/3']),
                (new EnumFilter('categoria_documento', 'App\Enums\Sapc\CategoriaDocumento'))->withMeta(['width' => 'w-1/3']),
                (new EnumFilter('tipo_formulario', 'App\Enums\Sapc\CertidaoPVLTipoModeloFormularioEnum'))->withMeta(['width' => 'w-1/3']),
            ]))->width('full'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }
}
