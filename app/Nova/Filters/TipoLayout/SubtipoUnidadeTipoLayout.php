<?php

namespace App\Nova\Filters\TipoLayout;

use App\Models\SubtipoUnidade;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Laravel\Nova\Http\Requests\NovaRequest;
use Outl1ne\NovaMultiselectFilter\MultiselectFilter;

class SubtipoUnidadeTipoLayout extends MultiselectFilter
{
    /**
     * The displayable name of the filter.
     *
     * @var string
     */
    public $name = 'Subtipo UG';

    /**
     * Apply the filter to the given query.
     *
     * @param  Builder  $query
     * @param  mixed  $value
     */
    public function apply(Request $request, $query, $value): Builder
    {
        return $query->whereRelation('tipoUnidades', 'subtipo_unidade_id', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        $tiposUnidades = SubtipoUnidade::orderBy('descricao')->pluck('descricao', 'id')->toArray();

        return $tiposUnidades;
    }
}
