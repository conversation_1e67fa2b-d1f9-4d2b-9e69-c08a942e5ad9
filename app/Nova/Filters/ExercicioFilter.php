<?php

namespace App\Nova\Filters;

use App\Traits\TodosExercicios;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ExercicioFilter extends Filter
{
    use TodosExercicios;

    /**
     * The displayable name of the filter.
     *
     * @var string
     */
    public $name = '<PERSON>er<PERSON><PERSON><PERSON>';

    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * \Laravel\Nova\Http\Requests\NovaRequest  $request
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->where('exercicio', $value);
    }

    /**
     * Get the filter's available options.
     *
     * \Laravel\Nova\Http\Requests\NovaRequest  $request
     *
     * @return array
     */
    public function options(NovaRequest $request)
    {

        return $this->todosExercicios()->toArray();
    }
}
