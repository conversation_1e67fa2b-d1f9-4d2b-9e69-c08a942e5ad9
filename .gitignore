storage/samlidp

/.cache
/.vagrant
/node_modules
/nova/.github
/nova/node_modules
/nova/vendor
/storage/*.key
/storage/debugbar/*
/vendor
/docker-auditoria/pgadmin/*
composer.lock

.env
.env.backup
.phpunit.result.cache
/storage/coverage.xml
/storage/junit.xml
Homestead.json
Homestead.yaml

/.idea
/.vscode
/.php_cs.cache
composer.phar
/public/assets
/public/css
/public/fonts
/public/hot
/public/js
/public/storage
/public/*.js
/public/*.json
/public/*.map
/public/.*
!/public/index.php
!/public/.htaccess

/public/reports/css
/public/reports/fonts
/public/reports/js
/public/reports/*.js
/public/reports/*.json
/public/reports/*.map

storage/framework/laravel-excel
storage/layouts-html
# --- gitignore metronic ---

.DS_Store
/dist

# local env files
.env.local
.env.*.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.php:Zone.Identifier
/_ide_helper.php
/_ide_helper_models.php
/.phpstorm.meta.php

/nova-components/*/node_modules
/nova-components/*/vendor
/public/8.js.LICENSE.txt
/public/7.js.LICENSE.txt
/tlint.json
/.php-cs-fixer.cache
docker-compose.yml
auth.json

# vite build
/public/build
.vite

# Puppeteer cache
.cache

# storybook
*storybook.log

# typescript
components.d.ts

components.json

/e2e

# Ignore lock files
yarn.lock
package-lock.json

# temp folder
resources/ts/temp
